/**
 * SISTEMA DE COMISSÃO EXTERNA
 * 
 * Este sistema permite receber comissões fora do Mercado Pago
 * através de PIX automático ou transferência bancária
 */

const axios = require('axios');
const systemCredentials = require('./system-credentials');

class ExternalCommissionSystem {
    constructor() {
        this.pendingCommissions = new Map();
        this.processedCommissions = new Map();
        this.totalCommissionsReceived = 0;
    }

    // Processar comissão externa após venda aprovada
    async processExternalCommission(paymentData, customCredentials = null) {
        try {
            const commissionAmount = paymentData.splitPayment.commissionAmount;
            const transactionId = paymentData.cartData.transactionId;

            console.log(`💰 Processando comissão externa: R$ ${commissionAmount.toFixed(2)} - ID: ${transactionId}`);

            // Usar credenciais customizadas se fornecidas
            this.currentCredentials = customCredentials;

            // Opção 1: PIX Automático
            if (this.isPixEnabled(customCredentials)) {
                await this.sendPixCommission(commissionAmount, transactionId, paymentData, customCredentials);
            }
            // Opção 2: Acumular para transferência manual
            else {
                await this.accumulateCommission(commissionAmount, transactionId, paymentData);
            }

        } catch (error) {
            console.error('Erro ao processar comissão externa:', error.message);
            // Salvar para reprocessamento posterior
            await this.saveFailedCommission(paymentData);
        }
    }

    // Verificar se PIX automático está habilitado
    isPixEnabled(customCredentials = null) {
        try {
            const credentials = customCredentials || systemCredentials.getSystemCredentials();
            return credentials.COMMISSION_RECEIVER.external_pix &&
                   credentials.COMMISSION_RECEIVER.external_pix !== 'SUA_CHAVE_PIX_AQUI' &&
                   credentials.COMMISSION_RECEIVER.external_pix !== 'CONFIGURE_SUA_CHAVE_PIX_AQUI';
        } catch (error) {
            return false;
        }
    }

    // Enviar comissão via PIX automático
    async sendPixCommission(amount, transactionId, paymentData, customCredentials = null) {
        try {
            const credentials = customCredentials || systemCredentials.getSystemCredentials();
            const pixKey = credentials.COMMISSION_RECEIVER.external_pix;

            // Usar API do Mercado Pago para enviar PIX
            const pixData = {
                transaction_amount: amount,
                description: `Comissão Sistema Vendas - ${transactionId}`,
                payment_method_id: 'pix',
                payer: {
                    email: credentials.COMMISSION_RECEIVER.email,
                    identification: {
                        type: 'CPF',
                        number: this.extractCpfFromPixKey(pixKey)
                    }
                },
                additional_info: {
                    items: [{
                        id: transactionId,
                        title: 'Comissão Sistema de Vendas',
                        description: `Comissão de R$ ${amount.toFixed(2)} da venda ${paymentData.product.name}`,
                        quantity: 1,
                        unit_price: amount
                    }]
                }
            };

            // Enviar PIX usando credenciais do sistema
            const response = await axios.post('https://api.mercadopago.com/v1/payments', pixData, {
                headers: {
                    'Authorization': `Bearer ${credentials.MASTER_ACCESS_TOKEN}`,
                    'Content-Type': 'application/json',
                    'X-Idempotency-Key': `COMMISSION_${transactionId}_${Date.now()}`
                }
            });

            if (response.data.status === 'approved') {
                console.log(`✅ PIX de comissão enviado: R$ ${amount.toFixed(2)} para ${pixKey}`);
                
                // Registrar comissão processada
                this.processedCommissions.set(transactionId, {
                    amount,
                    pixKey,
                    paymentId: response.data.id,
                    status: 'sent',
                    sentAt: new Date().toISOString(),
                    originalTransaction: paymentData
                });

                this.totalCommissionsReceived += amount;

                // Notificar sucesso
                await this.notifyCommissionSent(amount, pixKey, transactionId);

            } else {
                throw new Error(`PIX não aprovado: ${response.data.status}`);
            }

        } catch (error) {
            console.error('Erro ao enviar PIX de comissão:', error.message);
            // Fallback para acumulação
            await this.accumulateCommission(amount, transactionId, paymentData);
        }
    }

    // Acumular comissão para transferência manual
    async accumulateCommission(amount, transactionId, paymentData) {
        this.pendingCommissions.set(transactionId, {
            amount,
            transactionId,
            productName: paymentData.product.name,
            sellerName: paymentData.sellerId,
            buyerName: paymentData.buyerId,
            createdAt: new Date().toISOString(),
            status: 'pending'
        });

        console.log(`📊 Comissão acumulada: R$ ${amount.toFixed(2)} - Total pendente: R$ ${this.getTotalPendingCommissions().toFixed(2)}`);

        // Verificar se atingiu limite para transferência automática
        const totalPending = this.getTotalPendingCommissions();
        if (totalPending >= 50.00) { // R$ 50 mínimo para transferência
            await this.requestManualTransfer();
        }
    }

    // Obter total de comissões pendentes
    getTotalPendingCommissions() {
        let total = 0;
        for (const commission of this.pendingCommissions.values()) {
            if (commission.status === 'pending') {
                total += commission.amount;
            }
        }
        return total;
    }

    // Solicitar transferência manual
    async requestManualTransfer() {
        const totalPending = this.getTotalPendingCommissions();
        const credentials = systemCredentials.getSystemCredentials();

        console.log(`🏦 Solicitando transferência manual de R$ ${totalPending.toFixed(2)}`);

        // Gerar relatório para transferência
        const report = this.generateTransferReport();
        
        // Aqui você pode implementar:
        // 1. Envio de email automático
        // 2. Webhook para seu sistema
        // 3. Notificação no Discord
        // 4. API do seu banco

        console.log('📧 Relatório de transferência gerado:');
        console.log(report);

        // Marcar como solicitado
        for (const [id, commission] of this.pendingCommissions.entries()) {
            if (commission.status === 'pending') {
                commission.status = 'transfer_requested';
                commission.requestedAt = new Date().toISOString();
            }
        }
    }

    // Gerar relatório para transferência
    generateTransferReport() {
        const credentials = systemCredentials.getSystemCredentials();
        const pendingCommissions = Array.from(this.pendingCommissions.values())
            .filter(c => c.status === 'pending');

        const total = pendingCommissions.reduce((sum, c) => sum + c.amount, 0);

        return {
            transferDetails: {
                totalAmount: total,
                commissionsCount: pendingCommissions.length,
                generatedAt: new Date().toISOString()
            },
            bankAccount: credentials.COMMISSION_RECEIVER.bank_account,
            pixKey: credentials.COMMISSION_RECEIVER.external_pix,
            commissions: pendingCommissions.map(c => ({
                transactionId: c.transactionId,
                amount: c.amount,
                productName: c.productName,
                date: c.createdAt
            }))
        };
    }

    // Marcar transferência como concluída (chamado manualmente)
    async markTransferCompleted(transferAmount, transferReference) {
        let remainingAmount = transferAmount;
        const completedCommissions = [];

        for (const [id, commission] of this.pendingCommissions.entries()) {
            if (commission.status === 'transfer_requested' && remainingAmount >= commission.amount) {
                commission.status = 'completed';
                commission.completedAt = new Date().toISOString();
                commission.transferReference = transferReference;
                
                remainingAmount -= commission.amount;
                this.totalCommissionsReceived += commission.amount;
                completedCommissions.push(commission);

                // Mover para processadas
                this.processedCommissions.set(id, commission);
                this.pendingCommissions.delete(id);
            }
        }

        console.log(`✅ Transferência concluída: R$ ${transferAmount.toFixed(2)} - ${completedCommissions.length} comissões processadas`);
        return completedCommissions;
    }

    // Extrair CPF da chave PIX (se for CPF)
    extractCpfFromPixKey(pixKey) {
        // Se a chave PIX for um CPF, retornar apenas números
        if (/^\d{11}$/.test(pixKey.replace(/\D/g, ''))) {
            return pixKey.replace(/\D/g, '');
        }
        // Se não for CPF, retornar um CPF padrão (você deve configurar)
        return '00000000000'; // Configure seu CPF aqui
    }

    // Notificar comissão enviada
    async notifyCommissionSent(amount, pixKey, transactionId) {
        console.log(`🔔 Notificação: Comissão de R$ ${amount.toFixed(2)} enviada via PIX para ${pixKey}`);
        console.log(`📋 Transação: ${transactionId}`);
        console.log(`💰 Total recebido até agora: R$ ${this.totalCommissionsReceived.toFixed(2)}`);
    }

    // Salvar comissão que falhou para reprocessamento
    async saveFailedCommission(paymentData) {
        const transactionId = paymentData.cartData.transactionId;
        const amount = paymentData.splitPayment.commissionAmount;

        this.pendingCommissions.set(`FAILED_${transactionId}`, {
            amount,
            transactionId,
            status: 'failed',
            failedAt: new Date().toISOString(),
            paymentData,
            retryCount: 0
        });

        console.log(`❌ Comissão salva para reprocessamento: ${transactionId} - R$ ${amount.toFixed(2)}`);
    }

    // Obter relatório completo
    getCommissionReport() {
        const pending = Array.from(this.pendingCommissions.values());
        const processed = Array.from(this.processedCommissions.values());

        return {
            summary: {
                totalReceived: this.totalCommissionsReceived,
                totalPending: this.getTotalPendingCommissions(),
                processedCount: processed.length,
                pendingCount: pending.filter(c => c.status === 'pending').length,
                failedCount: pending.filter(c => c.status === 'failed').length
            },
            pending: pending,
            processed: processed
        };
    }
}

// Instância global
const externalCommissionSystem = new ExternalCommissionSystem();

module.exports = {
    ExternalCommissionSystem,
    externalCommissionSystem
};
