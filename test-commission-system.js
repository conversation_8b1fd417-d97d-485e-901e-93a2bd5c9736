/**
 * SISTEMA DE TESTES PARA COMISSÃO REAL
 * 
 * Execute este arquivo para testar todo o sistema de comissão
 * sem precisar de contas reais do Mercado Pago
 */

const { mpSimulator } = require('./test-simulation');

class CommissionSystemTester {
    constructor() {
        this.testResults = [];
        this.totalTests = 0;
        this.passedTests = 0;
    }

    // Executar todos os testes
    async runAllTests() {
        console.log('🧪 ===== INICIANDO TESTES DO SISTEMA DE COMISSÃO =====\n');

        await this.testSplitPaymentCreation();
        await this.testPixGeneration();
        await this.testPaymentApproval();
        await this.testCommissionCalculation();
        await this.testMultipleTransactions();
        await this.testBalanceReporting();

        this.showTestResults();
    }

    // Teste 1: Criação de Split Payment
    async testSplitPaymentCreation() {
        console.log('📋 Teste 1: Criação de Split Payment');
        
        try {
            const paymentData = {
                items: [{
                    title: 'Produto Teste',
                    quantity: 1,
                    unit_price: 100.00,
                    currency_id: 'BRL'
                }],
                marketplace_fee: 8.00, // 8% de comissão
                external_reference: 'TEST_001_PRODUCT_001_1703025598000'
            };

            const response = await mpSimulator.createPreference(paymentData, 'TEST_ACCESS_TOKEN');
            const preference = response.data;

            // Verificações
            this.assert(preference.id, 'Preferência deve ter ID');
            this.assert(preference.marketplace_fee === 8.00, 'Comissão deve ser R$ 8,00');
            this.assert(preference.split_details.total_amount === 100.00, 'Valor total deve ser R$ 100,00');
            this.assert(preference.split_details.seller_amount === 92.00, 'Vendedor deve receber R$ 92,00');

            console.log('✅ Teste 1 PASSOU - Split Payment criado corretamente\n');
            
        } catch (error) {
            console.log('❌ Teste 1 FALHOU:', error.message);
            this.testResults.push({ test: 'Split Payment Creation', status: 'FAILED', error: error.message });
        }
    }

    // Teste 2: Geração de PIX
    async testPixGeneration() {
        console.log('📋 Teste 2: Geração de PIX');
        
        try {
            // Criar preferência primeiro
            const paymentData = {
                items: [{ title: 'Produto PIX', quantity: 1, unit_price: 50.00, currency_id: 'BRL' }],
                marketplace_fee: 4.00
            };
            
            const prefResponse = await mpSimulator.createPreference(paymentData, 'TEST_ACCESS_TOKEN');
            const preferenceId = prefResponse.data.id;

            // Gerar PIX
            const pixResponse = await mpSimulator.generatePixPayment(preferenceId);
            const pixData = pixResponse.data;

            // Verificações
            this.assert(pixData.qr_code, 'PIX deve ter código QR');
            this.assert(pixData.qr_code_base64, 'PIX deve ter QR Code em base64');
            this.assert(pixData.ticket_url, 'PIX deve ter URL do ticket');

            console.log('✅ Teste 2 PASSOU - PIX gerado corretamente\n');
            
        } catch (error) {
            console.log('❌ Teste 2 FALHOU:', error.message);
            this.testResults.push({ test: 'PIX Generation', status: 'FAILED', error: error.message });
        }
    }

    // Teste 3: Aprovação de Pagamento
    async testPaymentApproval() {
        console.log('📋 Teste 3: Aprovação de Pagamento');
        
        try {
            // Criar preferência
            const paymentData = {
                items: [{ title: 'Produto Aprovação', quantity: 2, unit_price: 75.00, currency_id: 'BRL' }],
                marketplace_fee: 12.00 // 8% de R$ 150,00
            };
            
            const prefResponse = await mpSimulator.createPreference(paymentData, 'TEST_ACCESS_TOKEN');
            const preferenceId = prefResponse.data.id;

            // Aprovar pagamento
            const approvalResponse = await mpSimulator.approvePayment(preferenceId);
            const paymentData_approved = approvalResponse.data;

            // Verificações
            this.assert(paymentData_approved.status === 'approved', 'Pagamento deve estar aprovado');
            this.assert(paymentData_approved.transaction_amount === 150.00, 'Valor total deve ser R$ 150,00');
            this.assert(paymentData_approved.marketplace_fee === 12.00, 'Comissão deve ser R$ 12,00');
            this.assert(paymentData_approved.net_received_amount === 138.00, 'Vendedor deve receber R$ 138,00');

            console.log('✅ Teste 3 PASSOU - Pagamento aprovado e split processado\n');
            
        } catch (error) {
            console.log('❌ Teste 3 FALHOU:', error.message);
            this.testResults.push({ test: 'Payment Approval', status: 'FAILED', error: error.message });
        }
    }

    // Teste 4: Cálculo de Comissão
    async testCommissionCalculation() {
        console.log('📋 Teste 4: Cálculo de Comissão');
        
        try {
            const testCases = [
                { price: 100.00, expectedCommission: 8.00, expectedSeller: 92.00 },
                { price: 250.00, expectedCommission: 20.00, expectedSeller: 230.00 },
                { price: 37.50, expectedCommission: 3.00, expectedSeller: 34.50 },
                { price: 1000.00, expectedCommission: 80.00, expectedSeller: 920.00 }
            ];

            for (const testCase of testCases) {
                const commission = testCase.price * 0.08;
                const sellerAmount = testCase.price - commission;

                this.assert(
                    Math.abs(commission - testCase.expectedCommission) < 0.01, 
                    `Comissão de R$ ${testCase.price} deve ser R$ ${testCase.expectedCommission}`
                );
                
                this.assert(
                    Math.abs(sellerAmount - testCase.expectedSeller) < 0.01, 
                    `Vendedor de R$ ${testCase.price} deve receber R$ ${testCase.expectedSeller}`
                );
            }

            console.log('✅ Teste 4 PASSOU - Cálculos de comissão corretos\n');
            
        } catch (error) {
            console.log('❌ Teste 4 FALHOU:', error.message);
            this.testResults.push({ test: 'Commission Calculation', status: 'FAILED', error: error.message });
        }
    }

    // Teste 5: Múltiplas Transações
    async testMultipleTransactions() {
        console.log('📋 Teste 5: Múltiplas Transações');
        
        try {
            const transactions = [];
            let totalCommission = 0;
            let totalSellerAmount = 0;

            // Criar e aprovar 5 transações
            for (let i = 1; i <= 5; i++) {
                const price = 50 + (i * 10); // R$ 60, 70, 80, 90, 100
                const commission = price * 0.08;
                const sellerAmount = price - commission;

                totalCommission += commission;
                totalSellerAmount += sellerAmount;

                const paymentData = {
                    items: [{ title: `Produto ${i}`, quantity: 1, unit_price: price, currency_id: 'BRL' }],
                    marketplace_fee: commission
                };

                const prefResponse = await mpSimulator.createPreference(paymentData, 'TEST_ACCESS_TOKEN');
                const approvalResponse = await mpSimulator.approvePayment(prefResponse.data.id);
                
                transactions.push({
                    id: prefResponse.data.id,
                    price,
                    commission,
                    sellerAmount,
                    status: approvalResponse.data.status
                });
            }

            // Verificar relatório
            const report = mpSimulator.getBalanceReport();
            
            this.assert(transactions.length === 5, 'Deve ter 5 transações');
            this.assert(report.approved_transactions >= 5, 'Deve ter pelo menos 5 transações aprovadas');
            this.assert(Math.abs(report.system_balance - totalCommission) < 0.01, 'Saldo do sistema deve bater com comissões');

            console.log('✅ Teste 5 PASSOU - Múltiplas transações processadas corretamente\n');
            
        } catch (error) {
            console.log('❌ Teste 5 FALHOU:', error.message);
            this.testResults.push({ test: 'Multiple Transactions', status: 'FAILED', error: error.message });
        }
    }

    // Teste 6: Relatório de Saldos
    async testBalanceReporting() {
        console.log('📋 Teste 6: Relatório de Saldos');
        
        try {
            const report = mpSimulator.getBalanceReport();

            // Verificações
            this.assert(typeof report.system_balance === 'number', 'Saldo do sistema deve ser número');
            this.assert(typeof report.seller_balances === 'object', 'Saldos dos vendedores deve ser objeto');
            this.assert(typeof report.total_transactions === 'number', 'Total de transações deve ser número');
            this.assert(typeof report.approved_transactions === 'number', 'Transações aprovadas deve ser número');
            this.assert(report.system_balance > 0, 'Sistema deve ter saldo positivo após testes');

            console.log('✅ Teste 6 PASSOU - Relatórios funcionando corretamente\n');
            
        } catch (error) {
            console.log('❌ Teste 6 FALHOU:', error.message);
            this.testResults.push({ test: 'Balance Reporting', status: 'FAILED', error: error.message });
        }
    }

    // Função auxiliar para assertions
    assert(condition, message) {
        this.totalTests++;
        if (condition) {
            this.passedTests++;
            this.testResults.push({ test: message, status: 'PASSED' });
        } else {
            this.testResults.push({ test: message, status: 'FAILED' });
            throw new Error(message);
        }
    }

    // Mostrar resultados dos testes
    showTestResults() {
        console.log('🎯 ===== RESULTADOS DOS TESTES =====\n');
        
        console.log(`📊 Total de Testes: ${this.totalTests}`);
        console.log(`✅ Testes Passaram: ${this.passedTests}`);
        console.log(`❌ Testes Falharam: ${this.totalTests - this.passedTests}`);
        console.log(`📈 Taxa de Sucesso: ${((this.passedTests / this.totalTests) * 100).toFixed(1)}%\n`);

        // Mostrar relatório final
        const finalReport = mpSimulator.getBalanceReport();
        console.log('💰 ===== RELATÓRIO FINAL DE SALDOS =====');
        console.log(`Sistema: R$ ${finalReport.system_balance.toFixed(2)}`);
        console.log(`Vendedores: R$ ${Object.values(finalReport.seller_balances).reduce((a, b) => a + b, 0).toFixed(2)}`);
        console.log(`Transações: ${finalReport.total_transactions} (${finalReport.approved_transactions} aprovadas)\n`);

        if (this.passedTests === this.totalTests) {
            console.log('🎉 TODOS OS TESTES PASSARAM! Sistema de comissão funcionando perfeitamente!');
        } else {
            console.log('⚠️ Alguns testes falharam. Verifique a implementação.');
        }
    }
}

// Executar testes se arquivo for chamado diretamente
if (require.main === module) {
    const tester = new CommissionSystemTester();
    tester.runAllTests().catch(console.error);
}

module.exports = { CommissionSystemTester };
