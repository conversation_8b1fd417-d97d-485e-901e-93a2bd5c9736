/**
 * Sistema de Monetização Premium
 * Alternativa ao sistema de comissões
 */

const database = require('./database');

class PremiumSystem {
    constructor() {
        this.plans = {
            FREE: {
                name: 'Gratuito',
                price: 0,
                maxProducts: 2,
                maxSalesPerMonth: 20,
                features: {
                    // Recursos básicos
                    basicSales: true,
                    pixPayments: true,
                    dmDelivery: true,
                    basicSupport: true,

                    // Recursos premium (bloqueados)
                    cardPayments: false,
                    cryptoPayments: false,
                    logs: false,
                    moderation: false,
                    welcomeSystem: false,
                    ticketSystem: false,
                    inviteTracker: false,
                    advancedAuth: false,
                    cloudBackup: false,
                    antiFraud: false,
                    customization: false,
                    serverProtection: false,
                    autoRepost: false,
                    customCommands: false
                }
            },
            BASIC: {
                name: 'Basic',
                price: 29.97,
                maxProducts: 25,
                maxSalesPerMonth: 500,
                features: {
                    // Recursos básicos
                    basicSales: true,
                    pixPayments: true,
                    dmDelivery: true,
                    basicSupport: true,

                    // Recursos Basic
                    cardPayments: true,
                    cryptoPayments: true,
                    logs: true,
                    moderation: true,
                    welcomeSystem: true,
                    ticketSystem: true,
                    inviteTracker: true,
                    advancedAuth: true,
                    cloudBackup: true,
                    antiFraud: true,
                    customization: true,

                    // Recursos Pro (bloqueados)
                    serverProtection: false,
                    autoRepost: false,
                    customCommands: false
                }
            },
            PRO: {
                name: 'Pro',
                price: 49.97,
                maxProducts: -1, // Ilimitado
                maxSalesPerMonth: -1, // Ilimitado
                features: {
                    // Todos os recursos
                    basicSales: true,
                    pixPayments: true,
                    dmDelivery: true,
                    basicSupport: true,
                    cardPayments: true,
                    cryptoPayments: true,
                    logs: true,
                    moderation: true,
                    welcomeSystem: true,
                    ticketSystem: true,
                    inviteTracker: true,
                    advancedAuth: true,
                    cloudBackup: true,
                    antiFraud: true,
                    customization: true,
                    serverProtection: true,
                    autoRepost: true,
                    customCommands: true
                }
            }
        };
    }

    // Verificar plano do usuário
    getUserPlan(userId) {
        const userData = database.getUser(userId) || {};
        const premium = userData.premium || {};
        
        // Verificar se o plano ainda está ativo
        if (premium.expiresAt && new Date(premium.expiresAt) < new Date()) {
            // Plano expirado, voltar para FREE
            premium.plan = 'FREE';
            premium.expiresAt = null;
            userData.premium = premium;
            database.saveUser(userId, userData);
        }
        
        return premium.plan || 'FREE';
    }

    // Verificar se usuário pode criar mais produtos
    canCreateProduct(userId) {
        const plan = this.getUserPlan(userId);
        const planData = this.plans[plan];
        const userData = database.getUser(userId) || {};
        const currentProducts = userData.products ? userData.products.length : 0;
        
        if (planData.maxProducts === -1) return true; // Ilimitado
        return currentProducts < planData.maxProducts;
    }

    // Verificar se usuário pode fazer mais vendas este mês
    canMakeSale(userId) {
        const plan = this.getUserPlan(userId);
        const planData = this.plans[plan];
        
        if (planData.maxSalesPerMonth === -1) return true; // Ilimitado
        
        const userData = database.getUser(userId) || {};
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();
        
        // Contar vendas do mês atual
        const salesThisMonth = (userData.sales || []).filter(sale => {
            const saleDate = new Date(sale.date);
            return saleDate.getMonth() === currentMonth && saleDate.getFullYear() === currentYear;
        }).length;
        
        return salesThisMonth < planData.maxSalesPerMonth;
    }

    // Verificar se usuário tem acesso a uma feature
    hasFeature(userId, featureName) {
        const plan = this.getUserPlan(userId);
        const planData = this.plans[plan];
        return planData.features[featureName] || false;
    }

    // Ativar plano premium
    activatePlan(userId, planName, durationMonths = 1) {
        const userData = database.getUser(userId) || {};
        const expiresAt = new Date();
        expiresAt.setMonth(expiresAt.getMonth() + durationMonths);
        
        userData.premium = {
            plan: planName,
            activatedAt: new Date().toISOString(),
            expiresAt: expiresAt.toISOString(),
            durationMonths: durationMonths
        };
        
        database.saveUser(userId, userData);
        return true;
    }

    // Obter informações do plano
    getPlanInfo(planName) {
        return this.plans[planName] || this.plans.FREE;
    }

    // Obter todos os planos
    getAllPlans() {
        return this.plans;
    }

    // Verificar limites do usuário
    getUserLimits(userId) {
        const plan = this.getUserPlan(userId);
        const planData = this.plans[plan];
        const userData = database.getUser(userId) || {};
        
        const currentProducts = userData.products ? userData.products.length : 0;
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();
        
        const salesThisMonth = (userData.sales || []).filter(sale => {
            const saleDate = new Date(sale.date);
            return saleDate.getMonth() === currentMonth && saleDate.getFullYear() === currentYear;
        }).length;
        
        return {
            plan: plan,
            planName: planData.name,
            products: {
                current: currentProducts,
                max: planData.maxProducts,
                canCreate: this.canCreateProduct(userId)
            },
            sales: {
                current: salesThisMonth,
                max: planData.maxSalesPerMonth,
                canSell: this.canMakeSale(userId)
            },
            features: planData.features,
            premium: userData.premium || {}
        };
    }

    // Registrar venda para contagem
    registerSale(userId, saleData) {
        const userData = database.getUser(userId) || {};
        if (!userData.sales) userData.sales = [];
        
        userData.sales.push({
            ...saleData,
            date: new Date().toISOString()
        });
        
        database.saveUser(userId, userData);
    }

    // Verificar se precisa de upgrade
    needsUpgrade(userId, action) {
        switch (action) {
            case 'create_product':
                return !this.canCreateProduct(userId);
            case 'make_sale':
                return !this.canMakeSale(userId);
            case 'customization':
                return !this.hasFeature(userId, 'customization');
            case 'analytics':
                return !this.hasFeature(userId, 'analytics');
            case 'advanced_features':
                return !this.hasFeature(userId, 'advancedFeatures');
            default:
                return false;
        }
    }

    // Sugerir plano adequado
    suggestPlan(userId, action) {
        const currentPlan = this.getUserPlan(userId);
        
        switch (action) {
            case 'create_product':
                if (currentPlan === 'FREE') return 'BASIC';
                if (currentPlan === 'BASIC') return 'PRO';
                return 'ENTERPRISE';
            case 'customization':
                return currentPlan === 'FREE' ? 'BASIC' : 'PRO';
            case 'analytics':
                return 'PRO';
            case 'unlimited':
                return 'ENTERPRISE';
            default:
                return 'BASIC';
        }
    }
}

module.exports = new PremiumSystem();
