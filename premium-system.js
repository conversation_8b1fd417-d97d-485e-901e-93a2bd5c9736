/**
 * Sistema de Monetização Premium
 * Alternativa ao sistema de comissões
 */

const database = require('./database');

class PremiumSystem {
    constructor() {
        this.plans = {
            FREE: {
                name: 'Gratuito',
                price: 0,
                maxProducts: 3,
                maxSalesPerMonth: 50,
                features: {
                    basicCheckout: true,
                    dmDelivery: true,
                    stockManagement: true,
                    basicSupport: true,
                    customization: false,
                    analytics: false,
                    prioritySupport: false,
                    unlimitedProducts: false,
                    advancedFeatures: false
                }
            },
            BASIC: {
                name: 'Básico',
                price: 19.90,
                maxProducts: 15,
                maxSalesPerMonth: 200,
                features: {
                    basicCheckout: true,
                    dmDelivery: true,
                    stockManagement: true,
                    basicSupport: true,
                    customization: true,
                    analytics: false,
                    prioritySupport: false,
                    unlimitedProducts: false,
                    advancedFeatures: false
                }
            },
            PRO: {
                name: 'Profissional',
                price: 39.90,
                maxProducts: 50,
                maxSalesPerMonth: 1000,
                features: {
                    basicCheckout: true,
                    dmDelivery: true,
                    stockManagement: true,
                    basicSupport: true,
                    customization: true,
                    analytics: true,
                    prioritySupport: true,
                    unlimitedProducts: false,
                    advancedFeatures: true
                }
            },
            ENTERPRISE: {
                name: 'Empresarial',
                price: 79.90,
                maxProducts: -1, // Ilimitado
                maxSalesPerMonth: -1, // Ilimitado
                features: {
                    basicCheckout: true,
                    dmDelivery: true,
                    stockManagement: true,
                    basicSupport: true,
                    customization: true,
                    analytics: true,
                    prioritySupport: true,
                    unlimitedProducts: true,
                    advancedFeatures: true
                }
            }
        };
    }

    // Verificar plano do usuário
    getUserPlan(userId) {
        const userData = database.getUser(userId) || {};
        const premium = userData.premium || {};
        
        // Verificar se o plano ainda está ativo
        if (premium.expiresAt && new Date(premium.expiresAt) < new Date()) {
            // Plano expirado, voltar para FREE
            premium.plan = 'FREE';
            premium.expiresAt = null;
            userData.premium = premium;
            database.saveUser(userId, userData);
        }
        
        return premium.plan || 'FREE';
    }

    // Verificar se usuário pode criar mais produtos
    canCreateProduct(userId) {
        const plan = this.getUserPlan(userId);
        const planData = this.plans[plan];
        const userData = database.getUser(userId) || {};
        const currentProducts = userData.products ? userData.products.length : 0;
        
        if (planData.maxProducts === -1) return true; // Ilimitado
        return currentProducts < planData.maxProducts;
    }

    // Verificar se usuário pode fazer mais vendas este mês
    canMakeSale(userId) {
        const plan = this.getUserPlan(userId);
        const planData = this.plans[plan];
        
        if (planData.maxSalesPerMonth === -1) return true; // Ilimitado
        
        const userData = database.getUser(userId) || {};
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();
        
        // Contar vendas do mês atual
        const salesThisMonth = (userData.sales || []).filter(sale => {
            const saleDate = new Date(sale.date);
            return saleDate.getMonth() === currentMonth && saleDate.getFullYear() === currentYear;
        }).length;
        
        return salesThisMonth < planData.maxSalesPerMonth;
    }

    // Verificar se usuário tem acesso a uma feature
    hasFeature(userId, featureName) {
        const plan = this.getUserPlan(userId);
        const planData = this.plans[plan];
        return planData.features[featureName] || false;
    }

    // Ativar plano premium
    activatePlan(userId, planName, durationMonths = 1) {
        const userData = database.getUser(userId) || {};
        const expiresAt = new Date();
        expiresAt.setMonth(expiresAt.getMonth() + durationMonths);
        
        userData.premium = {
            plan: planName,
            activatedAt: new Date().toISOString(),
            expiresAt: expiresAt.toISOString(),
            durationMonths: durationMonths
        };
        
        database.saveUser(userId, userData);
        return true;
    }

    // Obter informações do plano
    getPlanInfo(planName) {
        return this.plans[planName] || this.plans.FREE;
    }

    // Obter todos os planos
    getAllPlans() {
        return this.plans;
    }

    // Verificar limites do usuário
    getUserLimits(userId) {
        const plan = this.getUserPlan(userId);
        const planData = this.plans[plan];
        const userData = database.getUser(userId) || {};
        
        const currentProducts = userData.products ? userData.products.length : 0;
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();
        
        const salesThisMonth = (userData.sales || []).filter(sale => {
            const saleDate = new Date(sale.date);
            return saleDate.getMonth() === currentMonth && saleDate.getFullYear() === currentYear;
        }).length;
        
        return {
            plan: plan,
            planName: planData.name,
            products: {
                current: currentProducts,
                max: planData.maxProducts,
                canCreate: this.canCreateProduct(userId)
            },
            sales: {
                current: salesThisMonth,
                max: planData.maxSalesPerMonth,
                canSell: this.canMakeSale(userId)
            },
            features: planData.features,
            premium: userData.premium || {}
        };
    }

    // Registrar venda para contagem
    registerSale(userId, saleData) {
        const userData = database.getUser(userId) || {};
        if (!userData.sales) userData.sales = [];
        
        userData.sales.push({
            ...saleData,
            date: new Date().toISOString()
        });
        
        database.saveUser(userId, userData);
    }

    // Verificar se precisa de upgrade
    needsUpgrade(userId, action) {
        switch (action) {
            case 'create_product':
                return !this.canCreateProduct(userId);
            case 'make_sale':
                return !this.canMakeSale(userId);
            case 'customization':
                return !this.hasFeature(userId, 'customization');
            case 'analytics':
                return !this.hasFeature(userId, 'analytics');
            case 'advanced_features':
                return !this.hasFeature(userId, 'advancedFeatures');
            default:
                return false;
        }
    }

    // Sugerir plano adequado
    suggestPlan(userId, action) {
        const currentPlan = this.getUserPlan(userId);
        
        switch (action) {
            case 'create_product':
                if (currentPlan === 'FREE') return 'BASIC';
                if (currentPlan === 'BASIC') return 'PRO';
                return 'ENTERPRISE';
            case 'customization':
                return currentPlan === 'FREE' ? 'BASIC' : 'PRO';
            case 'analytics':
                return 'PRO';
            case 'unlimited':
                return 'ENTERPRISE';
            default:
                return 'BASIC';
        }
    }
}

module.exports = new PremiumSystem();
