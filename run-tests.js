#!/usr/bin/env node

/**
 * SCRIPT PARA EXECUTAR TESTES DO SISTEMA DE COMISSÃO
 * 
 * Execute: node run-tests.js
 */

const { CommissionSystemTester } = require('./test-commission-system');

async function main() {
    console.log('🚀 Iniciando testes do sistema de comissão...\n');
    
    const tester = new CommissionSystemTester();
    await tester.runAllTests();
    
    console.log('\n✨ Testes concluídos!');
    console.log('\n📋 Para testar no Discord:');
    console.log('1. Configure suas credenciais em system-credentials.js');
    console.log('2. Inicie o bot: node index.js');
    console.log('3. Use /configurar para criar produtos');
    console.log('4. Faça uma compra teste');
    console.log('5. Verifique os logs de split payment');
}

main().catch(console.error);
