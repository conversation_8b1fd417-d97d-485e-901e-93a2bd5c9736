const { Client, GatewayIntentBits, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, Routes, ActivityType, StringSelectMenuBuilder, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');
const { REST } = require('@discordjs/rest');
const axios = require('axios');
const database = require('./database');
const antiCrashSystem = require('./anticrash-system');

class UserBot {
    constructor(userId, clientId, token) {
        this.userId = userId;
        this.clientId = clientId;
        this.token = token;
        this.client = null;
        this.isActive = false;

        // Sistema de navegação para controlar fluxo de embeds
        this.navigationStack = new Map(); // interactionId -> [page1, page2, page3...]
        this.currentPage = new Map(); // interactionId -> currentPageName

        // Cache de mensagens para painéis dinâmicos
        this.dynamicPanels = new Map(); // userId -> { messageId, channelId, type }
    }

    async start() {
        try {
            this.client = new Client({
                intents: [
                    GatewayIntentBits.Guilds,
                    GatewayIntentBits.GuildMessages,
                    GatewayIntentBits.MessageContent,
                    GatewayIntentBits.DirectMessages
                ]
            });

            // Eventos do bot
            this.client.once('ready', antiCrashSystem.wrapAsync(async () => {
                console.log(`Bot do usuário ${this.userId} está online como ${this.client.user.tag}`);

                // Definir status do bot
                this.client.user.setPresence({
                    activities: [{
                        name: 'Powered by: Nodex Solutions',
                        type: ActivityType.Watching
                    }],
                    status: 'online'
                });

                this.isActive = true;
                // Registrar comandos apenas nos servidores (instantâneo) ou globalmente se não estiver em servidores
                await this.registerCommandsOptimized();

                // Iniciar limpeza automática de carrinhos órfãos a cada 5 minutos
                setInterval(() => {
                    this.cleanupOrphanedCarts();
                }, 5 * 60 * 1000);
            }));

            // Handler de comandos e interações
            this.client.on('interactionCreate', antiCrashSystem.wrapAsync(async (interaction) => {
                if (interaction.isChatInputCommand()) {
                    await this.handleCommand(interaction);
                } else if (interaction.isStringSelectMenu()) {
                    await this.handleSelectMenu(interaction);
                } else if (interaction.isButton()) {
                    await this.handleButton(interaction);
                } else if (interaction.isModalSubmit()) {
                    await this.handleModalSubmit(interaction);
                }
            }));

            // Login
            await this.client.login(this.token);
            return true;
        } catch (error) {
            console.error(`Erro ao iniciar bot do usuário ${this.userId}:`, error);
            return false;
        }
    }

    // ===== SISTEMA DE PAINÉIS DINÂMICOS =====

    /**
     * Salva informações de um painel dinâmico
     */
    saveDynamicPanel(userId, messageId, channelId, type) {
        this.dynamicPanels.set(userId, {
            messageId,
            channelId,
            type,
            timestamp: Date.now()
        });
    }

    /**
     * Recupera informações de um painel dinâmico
     */
    getDynamicPanel(userId) {
        return this.dynamicPanels.get(userId) || null;
    }

    /**
     * Remove um painel dinâmico do cache
     */
    removeDynamicPanel(userId) {
        this.dynamicPanels.delete(userId);
    }

    /**
     * Atualiza um painel dinâmico existente
     */
    async updateDynamicPanel(userId, embed, components) {
        const panelInfo = this.getDynamicPanel(userId);
        if (!panelInfo) {
            console.log(`⚠️ Painel dinâmico não encontrado para usuário ${userId}`);
            return false;
        }

        try {
            const channel = await this.client.channels.fetch(panelInfo.channelId);
            const message = await channel.messages.fetch(panelInfo.messageId);

            await message.edit({
                embeds: [embed],
                components: components || []
            });

            return true;
        } catch (error) {
            console.error(`Erro ao atualizar painel dinâmico:`, error);
            // Remove painel inválido do cache
            this.removeDynamicPanel(userId);
            return false;
        }
    }

    // ===== SISTEMA DE NAVEGAÇÃO =====

    /**
     * Adiciona uma página ao stack de navegação
     */
    pushPage(interactionId, pageName) {
        if (!this.navigationStack.has(interactionId)) {
            this.navigationStack.set(interactionId, []);
        }

        const stack = this.navigationStack.get(interactionId);
        stack.push(pageName);
        this.currentPage.set(interactionId, pageName);
    }

    /**
     * Remove a página atual e volta para a anterior
     */
    popPage(interactionId) {
        if (!this.navigationStack.has(interactionId)) {
            return null;
        }

        const stack = this.navigationStack.get(interactionId);
        if (stack.length <= 1) {
            return null; // Não há página anterior
        }

        stack.pop(); // Remove página atual
        const previousPage = stack[stack.length - 1]; // Pega a anterior
        this.currentPage.set(interactionId, previousPage);
        return previousPage;
    }

    /**
     * Obtém a página atual
     */
    getCurrentPage(interactionId) {
        return this.currentPage.get(interactionId) || null;
    }

    /**
     * Limpa o stack de navegação
     */
    clearNavigation(interactionId) {
        this.navigationStack.delete(interactionId);
        this.currentPage.delete(interactionId);
    }

    /**
     * Navega para uma página específica usando o sistema de navegação
     */
    async navigateToPage(interaction, pageName, isBack = false) {
        const interactionId = interaction.id;

        if (!isBack) {
            this.pushPage(interactionId, pageName);
        }

        // Roteamento para diferentes páginas
        switch (pageName) {
            case 'auth_main':
                await this.handleAutenticacaoCommand(interaction);
                break;
            case 'mercado_pago_setup':
                await this.handleMercadoPagoSetup(interaction);
                break;
            case 'config_main':
                await this.handleConfigurarCommand(interaction);
                break;
            case 'produtos_config':
                await this.handleConfigProdutos(interaction);
                break;
            case 'precos_config':
                await this.handleConfigPrecos(interaction);
                break;
            case 'mensagens_config':
                await this.handleConfigMensagens(interaction);
                break;
            case 'canais_config':
                await this.handleConfigCanais(interaction);
                break;
            case 'roles_config':
                await this.handleConfigRoles(interaction);
                break;
            case 'mercado_pago_config':
                await this.handleMercadoPagoSetup(interaction);
                break;
            case 'nodex_pay_config':
                await this.handleNodexPayConfig(interaction);
                break;
            case 'auth_pix':
                await this.handleAuthPix(interaction);
                break;
            case 'alterar_botao':
                await this.handleAlterarBotaoPagamento(interaction);
                break;
            case 'add_categoria':
                await this.handleAdicionarCategoria(interaction);
                break;
            case 'add_produto':
                await this.handleAdicionarProduto(interaction);
                break;
            case 'ativar_cartao':
                await this.handleAtivarCartao(interaction);
                break;
            case 'ativar_pix':
                await this.handleAtivarPixTransparente(interaction);
                break;
            case 'config_api':
                await this.handleConfigurarAPI(interaction);
                break;
            case 'config_pagamentos':
                await this.handleConfigurarPagamentos(interaction);
                break;
            case 'config_loja':
                await this.handleConfigurarLoja(interaction);
                break;
            case 'personalizar_sistema':
                await this.handlePersonalizarSistema(interaction);
                break;
            case 'config_avancadas':
                await this.handleConfiguracoesAvancadas(interaction);
                break;
            case 'relatorios_analytics':
                await this.handleRelatoriosAnalytics(interaction);
                break;
            case 'suporte_ajuda':
                await this.handleSuporteAjuda(interaction);
                break;
            // Páginas de Autenticação
            case 'auth_metodos_pagamento':
                await this.handleAuthMetodosPagamento(interaction);
                break;
            case 'auth_gateways':
                await this.handleAuthGateways(interaction);
                break;
            case 'auth_seguranca':
                await this.handleAuthSeguranca(interaction);
                break;
            case 'auth_pagamentos_instantaneos':
                await this.handleAuthPagamentosInstantaneos(interaction);
                break;
            case 'auth_testes':
                await this.handleAuthTestes(interaction);
                break;
            case 'auth_monitoramento':
                await this.handleAuthMonitoramento(interaction);
                break;
            default:
                console.warn(`Página não encontrada: ${pageName}`);
        }
    }

    async registerCommandsOptimized() {
        const commands = [
            {
                name: 'autenticacao',
                description: 'Configurar método de pagamento para recebimentos automáticos'
            },
            {
                name: 'configurar',
                description: 'Configurar bot de vendas (produtos, preços, etc)'
            },
            {
                name: 'aprovar',
                description: 'Aprovar manualmente uma transação pendente',
                options: [
                    {
                        name: 'transacao_id',
                        description: 'ID da transação para aprovar (ex: TXN-1703025598000-A7B9C2)',
                        type: 3, // STRING
                        required: true
                    }
                ]
            }
        ];

        const rest = new REST({ version: '10' }).setToken(this.token);

        try {
            console.log(`🔄 Registrando ${commands.length} comandos para o bot do usuário ${this.userId}...`);
            console.log(`📋 Comandos: ${commands.map(cmd => `/${cmd.name}`).join(', ')}`);

            // Primeiro, limpar todos os comandos existentes (globais e de guild)
            await this.clearAllCommands(rest);

            // Aguardar um pouco antes de registrar novos comandos
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Verificar se o bot está em algum servidor
            const guilds = this.client.guilds.cache;

            if (guilds.size > 0) {
                // Se está em servidores, registrar apenas nos servidores (instantâneo)
                console.log(`🎯 Bot está em ${guilds.size} servidor(es). Registrando comandos nos servidores para aparição instantânea...`);

                for (const [guildId, guild] of guilds) {
                    try {
                        await rest.put(
                            Routes.applicationGuildCommands(this.clientId, guildId),
                            { body: commands }
                        );
                        console.log(`✅ Comandos registrados no servidor "${guild.name}"`);
                    } catch (error) {
                        console.error(`❌ Erro no servidor "${guild.name}":`, error.message);
                    }
                }

                console.log(`🎉 Comandos registrados em ${guilds.size} servidor(es) e devem aparecer INSTANTANEAMENTE!`);

            } else {
                // Se não está em servidores, registrar globalmente
                console.log(`🌐 Bot não está em servidores. Registrando comandos globalmente...`);

                await rest.put(
                    Routes.applicationCommands(this.clientId),
                    { body: commands }
                );

                console.log(`✅ Comandos registrados globalmente`);
                console.log(`⚠️  Nota: Comandos globais podem levar até 1 hora para aparecer. Convide o bot para um servidor para aparição instantânea.`);
            }

        } catch (error) {
            console.error(`❌ Erro ao registrar comandos para o bot do usuário ${this.userId}:`, error);

            if (error.code) {
                console.error(`   Código do erro: ${error.code}`);
            }
            if (error.message) {
                console.error(`   Mensagem: ${error.message}`);
            }
        }
    }

    async clearAllCommands(rest) {
        try {
            // Limpar comandos globais
            await rest.put(
                Routes.applicationCommands(this.clientId),
                { body: [] }
            );
            console.log(`🧹 Comandos globais limpos para o bot do usuário ${this.userId}`);

            // Limpar comandos de todos os servidores
            const guilds = this.client.guilds.cache;
            for (const [guildId, guild] of guilds) {
                try {
                    await rest.put(
                        Routes.applicationGuildCommands(this.clientId, guildId),
                        { body: [] }
                    );
                    console.log(`🧹 Comandos limpos no servidor "${guild.name}"`);
                } catch (error) {
                    // Ignorar erros de limpeza (pode não ter comandos para limpar)
                }
            }
        } catch (error) {
            console.error(`⚠️  Erro ao limpar comandos:`, error.message);
        }
    }



    async handleCommand(interaction) {
        try {
            const { commandName } = interaction;

            switch (commandName) {
                case 'autenticacao':
                    await this.handleAutenticacaoCommand(interaction);
                    break;
                case 'configurar':
                    await this.handleConfigurarCommand(interaction);
                    break;
                case 'aprovar':
                    await this.handleAprovarCommand(interaction);
                    break;
                default:
                    await interaction.reply({
                        content: 'Comando não reconhecido!',
                        ephemeral: true
                    });
            }
        } catch (error) {
            console.error(`Erro ao processar comando ${interaction.commandName}:`, error);
            antiCrashSystem.logError(`Erro no comando ${interaction.commandName}: ${error.message}`);

            try {
                if (!interaction.replied && !interaction.deferred) {
                    await interaction.reply({
                        content: '❌ Ocorreu um erro interno. Tente novamente em alguns segundos.',
                        ephemeral: true
                    });
                }
            } catch (replyError) {
                console.error('Erro ao enviar mensagem de erro:', replyError);
            }
        }
    }

    
    async handleAutenticacaoCommand(interaction) {
        // Inicializar navegação
        this.clearNavigation(interaction.id);
        this.pushPage(interaction.id, 'auth_main');

        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};

        // Verificar status das configurações
        const mercadoPagoConfigured = userData.mercadoPagoConfirmed ? '✅ Configurado' : '⚠️ Pendente';
        const nodexPayStatus = '🚧 Em Construção';

        // Status geral do sistema (apenas Mercado Pago por enquanto)
        const totalConfigured = userData.mercadoPagoConfirmed ? 1 : 0;
        const progressBar = totalConfigured === 1 ? '🟢🟢🟢' : '🔴🔴🔴';

        const embed = new EmbedBuilder()
            .setTitle('🔐 Central de Autenticação de Pagamentos')
            .setDescription('**Configure seus métodos de pagamento para começar a receber automaticamente!**\n\n' +
                          '💡 *Configure o Mercado Pago para ativar todas as funcionalidades do bot.*')
            .setColor(totalConfigured === 1 ? 0x00ff00 : 0xff4444)
            .addFields(
                {
                    name: '📊 Status da Configuração',
                    value: `${progressBar} **${totalConfigured === 1 ? 'Sistema Configurado!' : 'Aguardando Configuração'}**\n` +
                           `${totalConfigured === 1 ? '🎉 Pronto para receber pagamentos!' : '🚀 Configure o Mercado Pago para começar!'}`,
                    inline: false
                },
                {
                    name: '💳 Mercado Pago',
                    value: `**Status:** ${mercadoPagoConfigured}\n` +
                           `**Aceita:** Cartões, PIX, Boleto\n` +
                           `**Vantagens:** Taxas competitivas, aprovação rápida\n` +
                           `**Ideal para:** Vendas completas com múltiplos métodos`,
                    inline: true
                },
                {
                    name: '⚡ PIX Nodex Pay',
                    value: `**Status:** ${nodexPayStatus}\n` +
                           `**Aceita:** PIX instantâneo (em breve)\n` +
                           `**Vantagens:** Taxas reduzidas, recebimento direto\n` +
                           `**Previsão:** Lançamento em breve!`,
                    inline: true
                },
                {
                    name: '🎯 Próximos Passos',
                    value: totalConfigured === 1 ?
                           '✅ **Configuração completa!** Seu bot está pronto para vender.\n🚧 Aguarde o lançamento do Nodex Pay para ainda mais opções!' :
                           '1. **Configure o Mercado Pago** clicando no botão abaixo\n2. Siga as instruções de configuração\n3. Confirme as credenciais\n4. Comece a vender!',
                    inline: false
                }
            )
            .setFooter({ text: 'Powered by: Nodex Solutions | Configure o Mercado Pago para começar' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('auth_mercado_pago')
                    .setLabel(userData.mercadoPagoConfirmed ? 'Mercado Pago ✅' : 'Configurar Mercado Pago')
                    .setStyle(userData.mercadoPagoConfirmed ? ButtonStyle.Success : ButtonStyle.Primary)
                    .setEmoji('💳'),
                new ButtonBuilder()
                    .setCustomId('auth_pix')
                    .setLabel('Nodex Pay (Em Breve)')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🚧')
                    .setDisabled(true)
            );

        // Adicionar botão de ajuda se nenhum método estiver configurado
        if (totalConfigured === 0) {
            row.addComponents(
                new ButtonBuilder()
                    .setCustomId('auth_help')
                    .setLabel('Preciso de Ajuda')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('❓')
            );
        }

        // Verificar se é comando inicial ou navegação
        if (interaction.isChatInputCommand()) {
            // Para comando inicial ephemeral, não usar painel dinâmico
            await interaction.deferReply({
                ephemeral: true
            });

            // Editar a resposta com o conteúdo
            await interaction.editReply({ embeds: [embed], components: [row] });
        } else {
            // Para navegação em mensagens ephemeral, usar update direto
            await interaction.update({ embeds: [embed], components: [row] });
        }
    }

    async handleConfigurarCommand(interaction) {
        const userId = interaction.user.id;
        // Sempre ler o userData atualizado do banco
        const userData = database.getUser(userId) || {};

        // Inicializar navegação se for comando inicial
        if (interaction.isChatInputCommand()) {
            this.clearNavigation(interaction.id);
            this.pushPage(interaction.id, 'config_main');
        }

        // Verificar se o usuário já confirmou a autenticação do Mercado Pago
        if (!userData.mercadoPagoConfirmed || userData.mercadoPagoConfirmed === 'false' || userData.mercadoPagoConfirmed === false) {
            const embed = new EmbedBuilder()
                .setTitle('🔒 Acesso Negado')
                .setDescription('Você precisa configurar e confirmar suas credenciais de pagamento antes de acessar as configurações do bot.')
                .setColor(0xff0000)
                .addFields(
                    {
                        name: '📋 Requisitos:',
                        value: '• Configure suas credenciais do Mercado Pago\n• Confirme a configuração\n• Aguarde a validação das credenciais',
                        inline: false
                    },
                    {
                        name: '🔧 Como proceder:',
                        value: '1. Use o comando `/autenticacao`\n2. Configure suas credenciais do Mercado Pago\n3. Clique em "Confirmar" após inserir Access Token e Client Secret\n4. Aguarde a validação\n5. Tente novamente o comando `/configurar`',
                        inline: false
                    }
                )
                .setFooter({ text: 'Configure suas credenciais primeiro para continuar' })
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('start_auth_process')
                        .setLabel('Configurar Credenciais')
                        .setStyle(ButtonStyle.Primary)
                        .setEmoji('🔐')
                );

            // Verificar se é comando inicial ou navegação
            if (interaction.isChatInputCommand()) {
                await interaction.reply({ embeds: [embed], components: [row], ephemeral: true });
            } else {
           await interaction.update({ embeds: [embed], components: [row] });
            }
            return;
        }

        // Se chegou até aqui, o usuário está autenticado - mostrar menu principal de configurações
        const embed = new EmbedBuilder()
            .setTitle('⚙️ Painel de Configurações')
            .setDescription('Ebaaa 🥳🥳\nObrigado por escolher o meu serviço!\nEscolha uma categoria abaixo para configurar seu bot:')
            .setColor(0x5865F2) // Cor azul Discord
            .addFields(
                {
                    name: '💳 Configurar Pagamentos',
                    value: 'Configure métodos de pagamento, PIX, Mercado Pago e outras formas de recebimento.',
                    inline: true
                },
                {
                    name: '🏪 Configurar Loja',
                    value: 'Gerencie produtos, categorias, preços e configurações da sua loja virtual.',
                    inline: true
                },
                {
                    name: '🎨 Personalizar Sistema',
                    value: 'Customize cores, mensagens, botões e aparência do seu bot de vendas.',
                    inline: true
                },
                {
                    name: '🔧 Configurações Avançadas',
                    value: 'APIs, webhooks, integrações e configurações para desenvolvedores.',
                    inline: true
                },
                {
                    name: '📊 Relatórios e Analytics',
                    value: 'Visualize vendas, estatísticas e relatórios detalhados do seu bot.',
                    inline: true
                },
                {
                    name: '🆘 Suporte e Ajuda',
                    value: 'Documentação, tutoriais e canais de suporte técnico.',
                    inline: true
                }
            )
            .setFooter({ text: 'Powered by: Nodex Solutions | Selecione uma categoria para continuar' })
            .setTimestamp();

        // Primeira linha de botões - Categorias principais
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('config_pagamentos')
                    .setLabel('Configurar Pagamentos')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('💳'),
                new ButtonBuilder()
                    .setCustomId('config_loja')
                    .setLabel('Configurar Loja')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🏪'),
                new ButtonBuilder()
                    .setCustomId('personalizar_sistema')
                    .setLabel('Personalizar Sistema')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🎨')
            );

        // Segunda linha de botões - Categorias secundárias
        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('config_avancadas')
                    .setLabel('Configurações Avançadas')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔧'),
                new ButtonBuilder()
                    .setCustomId('relatorios_analytics')
                    .setLabel('Relatórios e Analytics')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📊'),
                new ButtonBuilder()
                    .setCustomId('suporte_ajuda')
                    .setLabel('Suporte e Ajuda')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🆘')
            );

        // Verificar se é comando inicial ou navegação
        if (interaction.isChatInputCommand()) {
            await interaction.reply({ embeds: [embed], components: [row1, row2], ephemeral: true });
        } else {
            await interaction.update({ embeds: [embed], components: [row1, row2] });
        }
    }

    async handleSelectMenu(interaction) {
        if (interaction.customId === 'payment_method_select') {
            const selectedValue = interaction.values[0];

            if (selectedValue === 'mercado_pago') {
                this.pushPage(interaction.id, 'mercado_pago_setup');
                await this.handleMercadoPagoSetup(interaction);
            } else if (selectedValue === 'nodex_pay') {
                this.pushPage(interaction.id, 'nodex_pay_config');
                await this.handleNodexPayConfig(interaction);
            }
        } else if (interaction.customId === 'select_category_for_simple_product') {
            const categoryId = interaction.values[0];
            const userId = interaction.user.id;
            const userData = database.getUser(userId) || {};
            const { productName } = this.tempProductData || {};

            if (!productName) {
                await interaction.update({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Erro')
                            .setDescription('Nome do produto não encontrado. Tente novamente.')
                            .setColor(0xff0000)
                    ],
                    components: []
                });
                setTimeout(async () => {
                    await this.handleGerenciarProdutos(interaction);
                }, 2000);
                return;
            }

            // Encontrar a categoria selecionada
            const category = userData.categories.find(cat => cat.id === categoryId);
            if (!category) {
                await interaction.update({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Categoria não encontrada')
                            .setDescription('A categoria selecionada não foi encontrada.')
                            .setColor(0xff0000)
                    ],
                    components: []
                });
                setTimeout(async () => {
                    await this.handleGerenciarProdutos(interaction);
                }, 2000);
                return;
            }

            try {
                const guild = interaction.guild;
                if (!guild) {
                    throw new Error('Não foi possível acessar o servidor.');
                }

                // Criar canal de texto para o produto dentro da categoria selecionada
                const productChannel = await guild.channels.create({
                    name: productName.toLowerCase().replace(/ /g, '-').replace(/[^a-z0-9-]/g, ''),
                    type: 0, // 0 é o tipo para canal de texto
                    parent: category.discordCategoryId,
                    permissionOverwrites: [
                        {
                            id: guild.id, // @everyone
                            allow: ['ViewChannel'],
                            deny: []
                        }
                    ]
                });

                // Criar o produto com dados básicos
                const newProduct = {
                    id: Date.now().toString(),
                    name: productName,
                    categoryId: category.id,
                    channelId: productChannel.id,
                    createdAt: new Date().toISOString(),
                    embedMessageId: null,
                    details: {
                        description: 'Descrição do produto não configurada',
                        image: 'https://via.placeholder.com/400x300/2196f3/ffffff?text=Produto',
                        banner: null,
                        thumbnail: null,
                        price: 0,
                        stock: 0,
                        color: 0x0099ff
                    }
                };

                if (!userData.products) {
                    userData.products = [];
                }
                userData.products.push(newProduct);
                category.products.push(newProduct.id);

                database.saveUser(userId, userData);

                // Enviar embed de configuração no canal do produto
                await this.sendProductConfigurationEmbed(productChannel, newProduct, userId);

                await interaction.update({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('✅ Produto Criado com Sucesso!')
                            .setDescription(`O produto **${productName}** foi criado!\n\n📋 **Detalhes:**\n• **Categoria:** ${category.name}\n• **Canal:** <#${productChannel.id}>\n\n🎨 Configure agora todos os detalhes do produto no canal criado!`)
                            .setColor(0x00ff00)
                    ],
                    components: []
                });

                // Não usar setTimeout com interaction já usada
                // setTimeout(async () => {
                //     await this.handleGerenciarProdutos(interaction);
                // }, 4000);

                // Limpar dados temporários
                this.tempProductData = null;

            } catch (error) {
                console.error(`Erro ao criar produto: ${error.message}`);
                await interaction.update({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Erro ao criar produto')
                            .setDescription(`Ocorreu um erro: ${error.message}`)
                            .setColor(0xff0000)
                    ],
                    components: []
                });
                setTimeout(async () => {
                    await this.handleGerenciarProdutos(interaction);
                }, 2000);
            }
        } else if (interaction.customId === 'select_category_for_product') {
            const categoryId = interaction.values[0];
            const userId = interaction.user.id;
            const userData = database.getUser(userId) || {};
            const { productName } = this.tempProductData || {};

            if (!productName) {
                await interaction.update({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Erro')
                            .setDescription('Nome do produto não encontrado. Tente novamente.')
                            .setColor(0xff0000)
                    ],
                    components: []
                });
                setTimeout(async () => {
                    await this.handleGerenciarProdutos(interaction);
                }, 2000);
                return;
            }
        } else if (interaction.customId === 'select_category_for_complete_product') {
            const categoryId = interaction.values[0];
            const userId = interaction.user.id;
            const userData = database.getUser(userId) || {};
            const { productName, productDescription, productPrice, productStock, productImage } = this.tempProductData || {};

            if (!productName || !productDescription || productPrice === undefined || productStock === undefined) {
                await interaction.update({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Erro')
                            .setDescription('Dados do produto não encontrados. Tente novamente.')
                            .setColor(0xff0000)
                    ],
                    components: []
                });
                setTimeout(async () => {
                    await this.handleGerenciarProdutos(interaction);
                }, 2000);
                return;
            }

            // Encontrar a categoria selecionada
            const category = userData.categories.find(cat => cat.id === categoryId);
            if (!category) {
                await interaction.update({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Categoria não encontrada!')
                            .setDescription('A categoria selecionada não foi encontrada.')
                            .setColor(0xff0000)
                    ],
                    components: []
                });
                setTimeout(async () => {
                    await this.handleGerenciarProdutos(interaction);
                }, 2000);
                return;
            }

            try {
                const guild = interaction.guild;
                if (!guild) {
                    throw new Error('Não foi possível acessar o servidor.');
                }

                // Criar canal de texto para o produto dentro da categoria selecionada
                const productChannel = await guild.channels.create({
                    name: productName.toLowerCase().replace(/ /g, '-').replace(/[^a-z0-9-]/g, ''), // Nome do canal limpo
                    type: 0, // 0 é o tipo para canal de texto
                    parent: category.discordCategoryId,
                    permissionOverwrites: [
                        {
                            id: guild.id, // @everyone
                            allow: ['ViewChannel'],
                            deny: []
                        }
                    ]
                });

                // Criar o produto completo com todos os dados
                const newProduct = {
                    id: Date.now().toString(),
                    name: productName,
                    categoryId: category.id,
                    channelId: productChannel.id,
                    createdAt: new Date().toISOString(),
                    embedMessageId: null, // Será preenchido após o envio da embed
                    details: {
                        description: productDescription,
                        image: productImage || 'https://via.placeholder.com/400x300/2196f3/ffffff?text=Produto',
                        price: productPrice,
                        stock: productStock
                    }
                };

                if (!userData.products) {
                    userData.products = [];
                }
                userData.products.push(newProduct);
                category.products.push(newProduct.id);

                database.saveUser(userId, userData);

                // Criar embed do produto com todos os dados
                const productEmbed = new EmbedBuilder()
                    .setTitle(`📦 ${productName}`)
                    .setDescription(productDescription)
                    .addFields(
                        { name: '💰 Preço', value: `R$ ${productPrice.toFixed(2).replace('.', ',')}`, inline: true },
                        { name: '📦 Estoque', value: `${productStock} unidades`, inline: true },
                        { name: '📂 Categoria', value: category.name, inline: true }
                    )
                    .setColor(0x00ff00)
                    .setFooter({ text: 'Use os botões abaixo para editar o produto.' })
                    .setTimestamp();

                // Adicionar imagem se fornecida
                if (productImage && productImage.trim() !== '') {
                    productEmbed.setImage(productImage);
                }

                // Botões de edição do produto
                const editButtons = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`edit_product_name_${newProduct.id}`)
                            .setLabel('✏️ Nome')
                            .setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder()
                            .setCustomId(`edit_product_description_${newProduct.id}`)
                            .setLabel('📝 Descrição')
                            .setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder()
                            .setCustomId(`edit_product_price_${newProduct.id}`)
                            .setLabel('💰 Preço')
                            .setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder()
                            .setCustomId(`edit_product_stock_${newProduct.id}`)
                            .setLabel('📦 Estoque')
                            .setStyle(ButtonStyle.Secondary),
                        new ButtonBuilder()
                            .setCustomId(`edit_product_image_${newProduct.id}`)
                            .setLabel('🖼️ Imagem')
                            .setStyle(ButtonStyle.Secondary)
                    );

                // Enviar a mensagem embed para o canal do produto
                const channel = guild.channels.cache.get(productChannel.id);
                if (channel) {
                    const message = await channel.send({ embeds: [productEmbed], components: [editButtons] });
                    newProduct.embedMessageId = message.id;
                    database.saveUser(userId, userData); // Salvar novamente com o ID da mensagem
                }

                await interaction.update({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('✅ Produto Criado com Sucesso!')
                            .setDescription(`O produto **${productName}** foi criado e configurado com sucesso!\n\n📋 **Detalhes:**\n• **Categoria:** ${category.name}\n• **Preço:** R$ ${productPrice.toFixed(2).replace('.', ',')}\n• **Estoque:** ${productStock} unidades\n• **Canal:** <#${productChannel.id}>\n\n🎉 Seu produto já está disponível para venda!`)
                            .setColor(0x00ff00)
                            .setThumbnail(productImage || null)
                    ],
                    components: []
                });

                setTimeout(async () => {
                    await this.handleGerenciarProdutos(interaction);
                }, 4000);

                // Limpar dados temporários
                this.tempProductData = null;

            } catch (error) {
                console.error(`Erro ao criar produto completo: ${error.message}`);
                await interaction.update({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Erro ao criar produto!')
                            .setDescription(`Ocorreu um erro ao criar o produto: ${error.message}`)
                            .setColor(0xff0000)
                    ],
                    components: []
                });
                setTimeout(async () => {
                    await this.handleGerenciarProdutos(interaction);
                }, 4000);

                // Limpar dados temporários
                this.tempProductData = null;

            }
        } else if (interaction.customId === 'select_categoria_remover') {
            const categoryId = interaction.values[0];
            const userId = interaction.user.id;
            const userData = database.getUser(userId) || {};
            
            // Verificar se o usuário tem categorias
            if (!userData.categories || userData.categories.length === 0) {
                await interaction.update({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Erro')
                            .setDescription('Você não possui categorias para remover.')
                            .setColor(0xff0000)
                    ],
                    components: []
                });
                
                setTimeout(async () => {
                    await this.handleGerenciarCategorias(interaction);
                }, 2000);
                return;
            }
            
            // Encontrar a categoria pelo ID
            const categoryIndex = userData.categories.findIndex(cat => cat.id === categoryId);
            if (categoryIndex === -1) {
                await interaction.update({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Erro')
                            .setDescription('Categoria não encontrada.')
                            .setColor(0xff0000)
                    ],
                    components: []
                });
                
                setTimeout(async () => {
                    await this.handleGerenciarCategorias(interaction);
                }, 2000);
                return;
            }
            
            const category = userData.categories[categoryIndex];
            
            // Criar botões de confirmação
            const confirmRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`confirmar_remover_categoria_${categoryId}`)
                        .setLabel('Confirmar Exclusão')
                        .setStyle(ButtonStyle.Danger)
                        .setEmoji('⚠️'),
                    new ButtonBuilder()
                        .setCustomId('cancelar_remover_categoria')
                        .setLabel('Cancelar')
                        .setStyle(ButtonStyle.Secondary)
                        .setEmoji('⬅️')
                );
            
            // Mostrar confirmação
            await interaction.update({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('⚠️ Confirmar Exclusão')
                        .setDescription(`Você está prestes a excluir a categoria **${category.name}**`)
                        .setColor(0xff0000)
                        .addFields(
                            {
                                name: '📊 Informações da categoria:',
                                value: `• **Nome:** ${category.name}\n• **Produtos:** ${category.products?.length || 0}\n• **Criada em:** ${new Date(category.createdAt).toLocaleDateString()}`,
                                inline: false
                            },
                            {
                                name: '⚠️ Atenção:',
                                value: 'Esta ação excluirá a categoria e todos os produtos associados a ela, além da categoria no Discord. Esta ação não pode ser desfeita!',
                                inline: false
                            }
                        )
                ],
                components: [confirmRow]
            });
        } else if (interaction.customId === 'select_product_to_edit') {
            const productId = interaction.values[0];
            await this.handleShowProductEditOptions(interaction, productId);
        } else if (interaction.customId === 'select_product_to_remove') {
            const productId = interaction.values[0];
            await this.handleConfirmProductRemoval(interaction, productId);
        } else if (interaction.customId.startsWith('select_new_category_')) {
            const productId = interaction.customId.replace('select_new_category_', '');
            const newCategoryId = interaction.values[0];
            await this.handleUpdateProductCategory(interaction, productId, newCategoryId);
        }
    }



    async handleNodexPayConfig(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('🚧 Nodex Pay - Em Construção')
            .setDescription('**O Nodex Pay ainda está em desenvolvimento!**\n\n⚡ *Estamos trabalhando para trazer a melhor experiência de pagamentos PIX para você.*')
            .setColor(0xffa500)
            .addFields(
                {
                    name: '🔮 O que está por vir:',
                    value: '• **PIX Instantâneo** - Recebimento em tempo real\n• **Taxas Reduzidas** - As menores do mercado\n• **Integração Simples** - Configuração em poucos cliques\n• **Dashboard Avançado** - Controle total dos seus recebimentos',
                    inline: false
                },
                {
                    name: '📅 Previsão de Lançamento:',
                    value: '🗓️ **Em breve!** Estamos nos últimos ajustes\n📧 Você será notificado quando estiver disponível',
                    inline: false
                },
                {
                    name: '💡 Enquanto isso:',
                    value: 'Configure o **Mercado Pago** para começar a receber pagamentos via PIX, cartão e boleto!',
                    inline: false
                }
            )
            .setFooter({ text: 'Fique ligado nas novidades! Em breve teremos grandes surpresas.' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('auth_mercado_pago')
                    .setLabel('Configurar Mercado Pago')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('💳'),
                new ButtonBuilder()
                    .setCustomId('contact_support_nodex')
                    .setLabel('Notificar quando Lançar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔔'),
                new ButtonBuilder()
                    .setCustomId('back_to_auth')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }

    async handleMercadoPagoSetup(interaction) {
        const userId = interaction.user.id;

        // Criar embed e componentes
        const { embed, components } = this.buildMercadoPagoConfigMessage(userId);

        // Para mensagens ephemeral, sempre usar update direto
        await interaction.update({ embeds: [embed], components: components });
    }

    async handleReloadMercadoPagoConfig(interaction) {
        const userId = interaction.user.id;

        // Mostrar indicador de carregamento
        await interaction.deferUpdate();

        // Aguardar um pouco para simular carregamento
        await new Promise(resolve => setTimeout(resolve, 500));

        // Atualizar a mensagem com os dados mais recentes
        // Como usamos deferUpdate(), a interação está marcada como deferred
        await this.updateMercadoPagoConfigMessage(interaction, userId);
    }

    
    async handleButton(interaction) {
        try {
            const { customId } = interaction;

            // Verificar se é um botão de configuração de produto
            if (customId.startsWith('config_product_name_')) {
                const productId = customId.replace('config_product_name_', '');
                await this.handleConfigProductName(interaction, productId);
                return;
            } else if (customId.startsWith('config_product_description_')) {
                const productId = customId.replace('config_product_description_', '');
                await this.handleConfigProductDescription(interaction, productId);
                return;
            } else if (customId.startsWith('config_product_price_')) {
                const productId = customId.replace('config_product_price_', '');
                await this.handleConfigProductPrice(interaction, productId);
                return;
            } else if (customId.startsWith('config_product_thumbnail_')) {
                const productId = customId.replace('config_product_thumbnail_', '');
                await this.handleConfigProductThumbnail(interaction, productId);
                return;
            } else if (customId.startsWith('config_product_banner_')) {
                const productId = customId.replace('config_product_banner_', '');
                await this.handleConfigProductBanner(interaction, productId);
                return;
            } else if (customId.startsWith('config_product_stock_add_')) {
                const productId = customId.replace('config_product_stock_add_', '');
                await this.handleConfigProductStockAdd(interaction, productId);
                return;
            } else if (customId.startsWith('config_product_stock_clear_')) {
                const productId = customId.replace('config_product_stock_clear_', '');
                await this.handleConfigProductStockClear(interaction, productId);
                return;
            } else if (customId.startsWith('config_product_color_')) {
                const productId = customId.replace('config_product_color_', '');
                await this.handleConfigProductColor(interaction, productId);
                return;
            } else if (customId.startsWith('config_product_buy_button_')) {
                const productId = customId.replace('config_product_buy_button_', '');
                await this.handleConfigProductBuyButton(interaction, productId);
                return;
            } else if (customId.startsWith('config_product_backup_')) {
                const productId = customId.replace('config_product_backup_', '');
                await this.handleConfigProductBackup(interaction, productId);
                return;
            } else if (customId.startsWith('config_product_cargo_')) {
                const productId = customId.replace('config_product_cargo_', '');
                await this.handleConfigProductCargo(interaction, productId);
                return;
            } else if (customId.startsWith('config_product_export_')) {
                const productId = customId.replace('config_product_export_', '');
                await this.handleConfigProductExport(interaction, productId);
                return;
            } else if (customId.startsWith('config_product_import_')) {
                const productId = customId.replace('config_product_import_', '');
                await this.handleConfigProductImport(interaction, productId);
                return;
            } else if (customId.startsWith('config_product_save_')) {
                const productId = customId.replace('config_product_save_', '');
                await this.handleConfigProductSave(interaction, productId);
                return;
            } else if (customId.startsWith('config_product_delete_')) {
                const productId = customId.replace('config_product_delete_', '');
                await this.handleConfigProductDelete(interaction, productId);
                return;
            } else if (customId.startsWith('confirm_clear_stock_')) {
                const productId = customId.replace('confirm_clear_stock_', '');
                await this.handleConfirmClearStock(interaction, productId);
                return;
            } else if (customId.startsWith('cancel_clear_stock_')) {
                await interaction.update({ content: '❌ Operação cancelada.', components: [] });
                return;
            } else if (customId.startsWith('confirm_delete_product_')) {
                const productId = customId.replace('confirm_delete_product_', '');
                await this.handleConfirmDeleteProduct(interaction, productId);
                return;
            } else if (customId.startsWith('cancel_delete_product_')) {
                await interaction.update({ content: '❌ Operação cancelada.', components: [] });
                return;
            } else if (customId.startsWith('set_color_')) {
                const parts = customId.split('_');
                const productId = parts[2];
                const color = parts[3];
                await this.handleSetProductColor(interaction, productId, color);
                return;
            } else if (customId.startsWith('buy_product_')) {
                const productId = customId.replace('buy_product_', '');
                await this.handleBuyProduct(interaction, productId);
                return;
            } else if (customId.startsWith('edit_product_settings_')) {
                const productId = customId.replace('edit_product_settings_', '');
                await this.handleEditProductSettings(interaction, productId);
                return;
            } else if (customId.startsWith('pay_mercado_pago_')) {
                const productId = customId.replace('pay_mercado_pago_', '');
                await this.handlePaymentMercadoPago(interaction, productId);
                return;
            } else if (customId.startsWith('pay_pix_')) {
                const productId = customId.replace('pay_pix_', '');
                await this.handlePaymentPix(interaction, productId);
                return;
            } else if (customId.startsWith('cancel_purchase_')) {
                await interaction.update({
                    content: '❌ Compra cancelada.',
                    embeds: [],
                    components: []
                });
                return;
            } else if (customId.startsWith('cart_payment_')) {
                const parts = customId.split('_');
                const productId = parts[2];
                const buyerId = parts[3];
                await this.handleCartPayment(interaction, productId, buyerId);
                return;
            } else if (customId.startsWith('cart_cancel_')) {
                const parts = customId.split('_');
                const productId = parts[2];
                const buyerId = parts[3];
                await this.handleCartCancel(interaction, productId, buyerId);
                return;
            } else if (customId.startsWith('cart_edit_quantity_')) {
                const parts = customId.split('_');
                const productId = parts[3];
                const buyerId = parts[4];
                await this.handleCartEditQuantity(interaction, productId, buyerId);
                return;
            } else if (customId.startsWith('cart_remove_product_')) {
                const parts = customId.split('_');
                const productId = parts[3];
                const buyerId = parts[4];
                await this.handleCartRemoveProduct(interaction, productId, buyerId);
                return;
            } else if (customId.startsWith('checkout_mercadopago_')) {
                const parts = customId.split('_');
                const productId = parts[2];
                const buyerId = parts[3];
                await this.handleCheckoutMercadoPago(interaction, productId, buyerId);
                return;
            } else if (customId.startsWith('checkout_whatsapp_')) {
                const parts = customId.split('_');
                const productId = parts[2];
                const buyerId = parts[3];
                await this.handleCheckoutWhatsapp(interaction, productId, buyerId);
                return;
            } else if (customId.startsWith('checkout_email_')) {
                const parts = customId.split('_');
                const productId = parts[2];
                const buyerId = parts[3];
                await this.handleCheckoutEmail(interaction, productId, buyerId);
                return;
            } else if (customId.startsWith('checkout_cancel_')) {
                const parts = customId.split('_');
                const productId = parts[2];
                const buyerId = parts[3];
                await this.handleCheckoutCancel(interaction, productId, buyerId);
                return;
            } else if (customId.startsWith('close_purchase_channel_')) {
                const parts = customId.split('_');
                const productId = parts[3];
                const buyerId = parts[4];
                await this.handleClosePurchaseChannel(interaction, productId, buyerId);
                return;
            } else if (customId.startsWith('generate_pix_')) {
                const parts = customId.split('_');
                const productId = parts[2];
                const buyerId = parts[3];
                const preferenceId = parts[4];
                await this.handleGeneratePix(interaction, productId, buyerId, preferenceId);
                return;
            } else if (customId.startsWith('copy_pix_')) {
                const preferenceId = customId.replace('copy_pix_', '');
                await this.handleCopyPix(interaction, preferenceId);
                return;
            } else if (customId.startsWith('check_payment_')) {
                const preferenceId = customId.replace('check_payment_', '');
                await this.handleCheckPayment(interaction, preferenceId);
                return;
            } else if (customId.startsWith('refund_payment_')) {
                const parts = customId.split('_');
                const buyerId = parts[2];
                const productId = parts[3];
                await this.handleRefundPayment(interaction, buyerId, productId);
                return;
            } else if (customId.startsWith('toggle_banco_')) {
                const bancoId = customId.replace('toggle_banco_', '');
                await this.handleToggleBanco(interaction, bancoId);
                return;
            } else if (customId === 'bloquear_todos_bancos') {
                await this.handleBloquearTodosBancos(interaction);
                return;
            } else if (customId === 'desbloquear_todos_bancos') {
                await this.handleDesbloquearTodosBancos(interaction);
                return;
            } else if (customId === 'back_to_config_pagamentos') {
                this.popPage(interaction.id);
                await this.handleConfigurarPagamentos(interaction);
                return;
            } else if (customId.startsWith('continue_pix_')) {
                const parts = customId.split('_');
                const productId = parts[2];
                const buyerId = parts[3];
                const preferenceId = parts[4];
                await this.handleContinuePix(interaction, productId, buyerId, preferenceId);
                return;
            } else if (customId.startsWith('cancel_pix_')) {
                await interaction.update({
                    content: '❌ **Geração de PIX cancelada.**\n\nVocê pode tentar novamente quando quiser.',
                    embeds: [],
                    components: []
                });
                return;
            } else if (customId.startsWith('confirm_manual_approval_')) {
                const transacaoId = customId.replace('confirm_manual_approval_', '');
                await this.handleConfirmManualApproval(interaction, transacaoId);
                return;
            } else if (customId.startsWith('cancel_manual_approval_')) {
                const transacaoId = customId.replace('cancel_manual_approval_', '');
                await this.handleCancelManualApproval(interaction, transacaoId);
                return;
            }

            // Verificar se é um botão de edição de produto (sistema antigo)
            else if (customId.startsWith('edit_product_name_')) {
                const productId = customId.replace('edit_product_name_', '');
                await this.handleEditProductName(interaction, productId);
                return;
            } else if (customId.startsWith('edit_product_description_')) {
                const productId = customId.replace('edit_product_description_', '');
                await this.handleEditProductDescription(interaction, productId);
                return;
            } else if (customId.startsWith('edit_product_image_')) {
                const productId = customId.replace('edit_product_image_', '');
                await this.handleEditProductImage(interaction, productId);
                return;
            } else if (customId.startsWith('edit_product_price_')) {
                const productId = customId.replace('edit_product_price_', '');
                await this.handleEditProductPrice(interaction, productId);
                return;
            } else if (customId.startsWith('edit_product_stock_')) {
                const productId = customId.replace('edit_product_stock_', '');
                await this.handleEditProductStock(interaction, productId);
                return;
            } else if (customId.startsWith('edit_product_category_')) {
                const productId = customId.replace('edit_product_category_', '');
                await this.handleEditProductCategory(interaction, productId);
                return;
            }

            // Verificar se é um botão de confirmação de remoção de categoria
            if (customId.startsWith('confirmar_remover_categoria_')) {
                const categoryId = customId.replace('confirmar_remover_categoria_', '');
                await this.handleConfirmarRemoverCategoria(interaction, categoryId);
                return;
            }

            // Verificar se é um botão de confirmação de remoção de produto
            if (customId.startsWith('confirm_remove_product_')) {
                const productId = customId.replace('confirm_remove_product_', '');
                await this.handleExecuteProductRemoval(interaction, productId);
                return;
            }

        switch (customId) {
            case 'setup_mercado_pago':
                this.pushPage(interaction.id, 'mercado_pago_setup');
                await this.handleMercadoPagoSetup(interaction);
                break;
            case 'contact_support_mp':
                await this.handleContactSupport(interaction, 'Mercado Pago');
                break;
            case 'contact_support_nodex':
                await this.handleContactSupport(interaction, 'Nodex Pay');
                break;
            case 'back_to_auth':
                await this.handleBackNavigation(interaction, 'auth_main');
                break;
            case 'reload_mercado_pago_config':
                await this.handleReloadMercadoPagoConfig(interaction);
                break;
            case 'setup_access_token':
                await this.handleSetupAccessToken(interaction);
                break;
            case 'setup_client_secret':
                await this.handleSetupClientSecret(interaction);
                break;
            case 'confirm_mercado_pago':
                await this.handleConfirmMercadoPago(interaction);
                break;
            case 'clear_mercado_pago_credentials':
                await this.handleClearMercadoPagoCredentials(interaction);
                break;
            case 'contact_support_access_token':
                await this.handleContactSupport(interaction, 'Access Token do Mercado Pago');
                break;
            case 'contact_support_client_secret':
                await this.handleContactSupport(interaction, 'Client Secret do Mercado Pago');
                break;
            case 'back_to_mercado_pago':
                await this.handleBackNavigation(interaction, 'mercado_pago_setup');
                break;
            case 'back_to_mercado_pago_config':
                await this.handleBackNavigation(interaction, 'mercado_pago_config');
                break;
            case 'contact_priority_support':
                await this.handleContactSupport(interaction, 'Suporte Prioritário - Configuração de Pagamentos');
                break;
            case 'mercado_pago_help':
                await this.handleMercadoPagoHelp(interaction);
                break;
                        case 'start_auth_process':
                await this.handleAutenticacaoCommand(interaction);
                break;
            case 'auth_mercado_pago':
                this.pushPage(interaction.id, 'mercado_pago_setup');
                await this.handleMercadoPagoSetup(interaction);
                break;
            case 'auth_pix':
                this.pushPage(interaction.id, 'auth_pix');
                await this.handleAuthPix(interaction);
                break;
            case 'auth_help':
                await this.handleAuthHelp(interaction);
                break;
            case 'test_mercado_pago_connection':
                await this.handleTestMercadoPagoConnection(interaction);
                break;
            case 'criar_produto':
                await this.handleCriarProduto(interaction);
                break;
            case 'editar_produto':
                await this.handleEditarProduto(interaction);
                break;
            case 'remover_produto':
                await this.handleRemoverProduto(interaction);
                break;
            case 'config_produtos':
                this.pushPage(interaction.id, 'produtos_config');
                await this.handleConfigProdutos(interaction);
                break;
            case 'config_precos':
                this.pushPage(interaction.id, 'precos_config');
                await this.handleConfigPrecos(interaction);
                break;
            case 'config_mensagens':
                this.pushPage(interaction.id, 'mensagens_config');
                await this.handleConfigMensagens(interaction);
                break;
            case 'config_canais':
                this.pushPage(interaction.id, 'canais_config');
                await this.handleConfigCanais(interaction);
                break;
            case 'config_roles':
                this.pushPage(interaction.id, 'roles_config');
                await this.handleConfigRoles(interaction);
                break;
            case 'back_to_config':
                await this.handleBackNavigation(interaction, 'config_main');
                break;
            case 'config_pagamentos':
                this.pushPage(interaction.id, 'config_pagamentos');
                await this.handleConfigurarPagamentos(interaction);
                break;
            case 'config_loja':
                this.pushPage(interaction.id, 'config_loja');
                await this.handleConfigurarLoja(interaction);
                break;
            case 'personalizar_sistema':
                this.pushPage(interaction.id, 'personalizar_sistema');
                await this.handlePersonalizarSistema(interaction);
                break;
            case 'config_avancadas':
                this.pushPage(interaction.id, 'config_avancadas');
                await this.handleConfiguracoesAvancadas(interaction);
                break;
            case 'relatorios_analytics':
                this.pushPage(interaction.id, 'relatorios_analytics');
                await this.handleRelatoriosAnalytics(interaction);
                break;
            case 'suporte_ajuda':
                this.pushPage(interaction.id, 'suporte_ajuda');
                await this.handleSuporteAjuda(interaction);
                break;
            // Subcategorias de Pagamentos
            case 'habilitar_pix':
                this.pushPage(interaction.id, 'habilitar_pix');
                await this.handleHabilitarPix(interaction);
                break;
            case 'autenticar_pix':
                this.pushPage(interaction.id, 'autenticar_pix');
                await this.handleAutenticarPix(interaction);
                break;
            case 'config_mercado_pago':
                this.pushPage(interaction.id, 'config_mercado_pago');
                await this.handleConfigMercadoPago(interaction);
                break;
            case 'ativar_cartao_credito':
                this.pushPage(interaction.id, 'ativar_cartao_credito');
                await this.handleAtivarCartao(interaction);
                break;
            case 'modificar_dados_mp':
                this.pushPage(interaction.id, 'modificar_dados_mp');
                await this.handleModificarDadosMP(interaction);
                break;
            case 'visualizar_dados_mp':
                this.pushPage(interaction.id, 'modificar_dados_mp');
                await this.handleModificarDadosMP(interaction);
                break;
            case 'toggle_site_pagamento':
                await this.handleToggleSitePagamento(interaction);
                break;
            case 'toggle_sistema':
                await this.handleToggleSistema(interaction);
                break;
            case 'config_taxas_pagamento':
                this.pushPage(interaction.id, 'config_taxas_pagamento');
                await this.handleConfigTaxasPagamento(interaction);
                break;
            case 'gerenciar_bancos_bloqueados':
                this.pushPage(interaction.id, 'gerenciar_bancos_bloqueados');
                await this.handleGerenciarBancosBloqueados(interaction);
                break;
            case 'gerenciar_cupons':
                this.pushPage(interaction.id, 'gerenciar_cupons');
                await this.handleGerenciarCupons(interaction);
                break;
            // Subcategorias de Loja
            case 'gerenciar_produtos':
                this.pushPage(interaction.id, 'gerenciar_produtos');
                await this.handleGerenciarProdutos(interaction);
                break;
            case 'gerenciar_categorias':
                this.pushPage(interaction.id, 'gerenciar_categorias');
                await this.handleGerenciarCategorias(interaction);
                break;
            case 'criar_nova_categoria':
                await this.handleCriarNovaCategoria(interaction);
                break;
            case 'remover_categoria':
                await this.handleRemoverCategoria(interaction);
                break;
            case 'cancelar_remover_categoria':
                await this.handleGerenciarCategorias(interaction);
                break;
            // Categorias de Autenticação
            case 'auth_metodos_pagamento':
                this.pushPage(interaction.id, 'auth_metodos_pagamento');
                await this.handleAuthMetodosPagamento(interaction);
                break;
            case 'auth_gateways':
                this.pushPage(interaction.id, 'auth_gateways');
                await this.handleAuthGateways(interaction);
                break;
            case 'auth_seguranca':
                this.pushPage(interaction.id, 'auth_seguranca');
                await this.handleAuthSeguranca(interaction);
                break;
            case 'auth_pagamentos_instantaneos':
                this.pushPage(interaction.id, 'auth_pagamentos_instantaneos');
                await this.handleAuthPagamentosInstantaneos(interaction);
                break;
            case 'auth_testes':
                this.pushPage(interaction.id, 'auth_testes');
                await this.handleAuthTestes(interaction);
                break;
            case 'auth_monitoramento':
                this.pushPage(interaction.id, 'auth_monitoramento');
                await this.handleAuthMonitoramento(interaction);
                break;
            // Navegação de volta para autenticação
            case 'back_to_auth_main':
                await this.handleBackNavigation(interaction, 'auth_main');
                break;
            // Navegação de volta para categorias
            case 'back_to_config_pagamentos':
                await this.handleBackNavigation(interaction, 'config_pagamentos');
                break;
            case 'back_to_config_loja':
                await this.handleBackNavigation(interaction, 'config_loja');
                break;
            case 'back_to_personalizar_sistema':
                await this.handleBackNavigation(interaction, 'personalizar_sistema');
                break;
            default:
                await interaction.reply({
                    content: 'Botão não reconhecido!',
                    ephemeral: true
                });
        }
        } catch (error) {
            console.error(`Erro ao processar botão ${interaction.customId}:`, error);
            antiCrashSystem.logError(`Erro no botão ${interaction.customId}: ${error.message}`);

            try {
                if (!interaction.replied && !interaction.deferred) {
                    await interaction.reply({
                        content: '❌ Ocorreu um erro interno. Tente novamente em alguns segundos.',
                        ephemeral: true
                    });
                } else if (interaction.deferred) {
                    await interaction.editReply({
                        content: '❌ Ocorreu um erro interno. Tente novamente em alguns segundos.'
                    });
                }
            } catch (replyError) {
                console.error('Erro ao enviar mensagem de erro:', replyError);
            }
        }
    }

    /**
     * Manipula navegação de volta usando o sistema de navegação
     */
    async handleBackNavigation(interaction, targetPage) {
        const interactionId = interaction.id;

        // Se temos um stack de navegação, usar a página anterior
        const previousPage = this.popPage(interactionId);
        if (previousPage) {
            await this.navigateToPage(interaction, previousPage, true);
        } else {
            // Fallback para página específica se não há stack
            await this.navigateToPage(interaction, targetPage, true);
        }
    }

    async handleContactSupport(interaction, paymentMethod) {
        const embed = new EmbedBuilder()
            .setTitle('🆘 Suporte - ' + paymentMethod)
            .setDescription(`Nossa equipe irá ajudá-lo a configurar o ${paymentMethod} para receber pagamentos automáticos.`)
            .setColor(0xff6600)
            .addFields(
                {
                    name: '📧 Como proceder:',
                    value: '1. Entre em contato com nosso suporte\n2. Informe que deseja configurar: **' + paymentMethod + '**\n3. Tenha suas credenciais em mãos\n4. Aguarde a configuração ser finalizada',
                    inline: false
                },
                {
                    name: '💬 Canais de Suporte:',
                    value: '• **Discord:** Abra um ticket\n• **Email:** <EMAIL>\n• **WhatsApp:** +55 11 99999-9999',
                    inline: false
                },
                {
                    name: '⏰ Horário de Atendimento:',
                    value: 'Segunda a Sexta: 08:00 às 18:00\nSábados: 08:00 às 12:00\nDomingos: Emergências apenas',
                    inline: false
                }
            )
            .setFooter({ text: 'Powered by: Nodex Solutions' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('back_to_auth')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }

    async handleBackToAuth(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('🔐 Configuração de Pagamentos')
            .setDescription('Selecione o método de pagamento que deseja configurar para receber pagamentos automáticos:')
            .setColor(0x9b59b6)
            .addFields(
                {
                    name: '💳 Mercado Pago',
                    value: 'Configure sua conta do Mercado Pago para receber pagamentos via cartão, PIX e boleto',
                    inline: false
                },
                {
                    name: '🏦 Nodex Pay (PIX)',
                    value: 'Configure sua chave PIX para receber pagamentos instantâneos via Nodex Pay',
                    inline: false
                }
            )
            .setFooter({ text: 'Selecione uma opção abaixo para continuar' })
            .setTimestamp();

        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('payment_method_select')
            .setPlaceholder('Escolha o método de pagamento')
            .addOptions([
                {
                    label: 'Mercado Pago',
                    description: 'Configurar conta do Mercado Pago',
                    value: 'mercado_pago',
                    emoji: '💳'
                },
                {
                    label: 'Nodex Pay (PIX)',
                    description: 'Configurar chave PIX via Nodex Pay',
                    value: 'nodex_pay',
                    emoji: '🏦'
                }
            ]);

        const row = new ActionRowBuilder()
            .addComponents(selectMenu);

        await interaction.update({ embeds: [embed], components: [row] });
    }

    
    async handleSetupAccessToken(interaction) {
        const modal = new ModalBuilder()
            .setCustomId('access_token_modal')
            .setTitle('Configurar Access Token - Mercado Pago');

        const accessTokenInput = new TextInputBuilder()
            .setCustomId('access_token_input')
            .setLabel('Access Token do Mercado Pago')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('APP_USR-1234567890-123456-abcdef1234567890abcdef1234567890-123456789')
            .setRequired(true)
            .setMinLength(50)
            .setMaxLength(200);

        const firstActionRow = new ActionRowBuilder().addComponents(accessTokenInput);
        modal.addComponents(firstActionRow);

        await interaction.showModal(modal);
    }

    async handleSetupClientSecret(interaction) {
        const modal = new ModalBuilder()
            .setCustomId('client_secret_modal')
            .setTitle('Configurar Client Secret - Mercado Pago');

        const clientSecretInput = new TextInputBuilder()
            .setCustomId('client_secret_input')
            .setLabel('Client Secret do Mercado Pago')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('APP_USR-... ou ')
            .setRequired(true)
            .setMinLength(32)
            .setMaxLength(200);

        const firstActionRow = new ActionRowBuilder().addComponents(clientSecretInput);
        modal.addComponents(firstActionRow);

        await interaction.showModal(modal);
    }

    async handleModalSubmit(interaction) {
        const { customId, user } = interaction;
        const userId = user.id;

        if (customId.startsWith('edit_product_name_modal_')) {
            const productId = customId.replace('edit_product_name_modal_', '');
            const newName = interaction.fields.getTextInputValue('product_name_input');
            await this.handleUpdateProductDetails(interaction, productId, 'name', newName);
        } else if (customId.startsWith('edit_product_description_modal_')) {
            const productId = customId.replace('edit_product_description_modal_', '');
            const newDescription = interaction.fields.getTextInputValue('product_description_input');
            await this.handleUpdateProductDetails(interaction, productId, 'description', newDescription);
        } else if (customId.startsWith('edit_product_image_modal_')) {
            const productId = customId.replace('edit_product_image_modal_', '');
            const newImage = interaction.fields.getTextInputValue('product_image_input');
            await this.handleUpdateProductDetails(interaction, productId, 'image', newImage);
        } else if (customId.startsWith('edit_product_price_modal_')) {
            const productId = customId.replace('edit_product_price_modal_', '');
            const newPrice = parseFloat(interaction.fields.getTextInputValue('product_price_input'));
            if (isNaN(newPrice) || newPrice < 0) {
                await interaction.reply({ content: 'Preço inválido. Por favor, insira um número válido.', ephemeral: true });
                return;
            }
            await this.handleUpdateProductDetails(interaction, productId, 'price', newPrice);
        } else if (customId.startsWith('edit_product_stock_modal_')) {
            const productId = customId.replace('edit_product_stock_modal_', '');
            const newStock = parseInt(interaction.fields.getTextInputValue('product_stock_input'), 10);
            if (isNaN(newStock) || newStock < 0) {
                await interaction.reply({ content: 'Estoque inválido. Por favor, insira um número inteiro válido.', ephemeral: true });
                return;
            }
            await this.handleUpdateProductDetails(interaction, productId, 'stock', newStock);
        } else if (customId.startsWith('config_product_name_modal_')) {
            const productId = customId.replace('config_product_name_modal_', '');
            const newName = interaction.fields.getTextInputValue('product_name_input');
            await this.handleUpdateProductConfigDetails(interaction, productId, 'name', newName);
        } else if (customId.startsWith('config_product_description_modal_')) {
            const productId = customId.replace('config_product_description_modal_', '');
            const newDescription = interaction.fields.getTextInputValue('product_description_input');
            await this.handleUpdateProductConfigDetails(interaction, productId, 'description', newDescription);
        } else if (customId.startsWith('config_product_price_modal_')) {
            const productId = customId.replace('config_product_price_modal_', '');
            const newPrice = parseFloat(interaction.fields.getTextInputValue('product_price_input'));
            if (isNaN(newPrice) || newPrice < 0) {
                await interaction.reply({ content: 'Preço inválido. Por favor, insira um número válido.', ephemeral: true });
                return;
            }
            await this.handleUpdateProductConfigDetails(interaction, productId, 'price', newPrice);
        } else if (customId.startsWith('config_product_thumbnail_modal_')) {
            const productId = customId.replace('config_product_thumbnail_modal_', '');
            const newThumbnail = interaction.fields.getTextInputValue('product_thumbnail_input');
            await this.handleUpdateProductConfigDetails(interaction, productId, 'thumbnail', newThumbnail);
        } else if (customId.startsWith('config_product_banner_modal_')) {
            const productId = customId.replace('config_product_banner_modal_', '');
            const newBanner = interaction.fields.getTextInputValue('product_banner_input');
            await this.handleUpdateProductConfigDetails(interaction, productId, 'banner', newBanner);
        } else if (customId.startsWith('config_product_buy_button_modal_')) {
            const productId = customId.replace('config_product_buy_button_modal_', '');
            const newBuyButtonText = interaction.fields.getTextInputValue('product_buy_button_text_input');
            const newBuyButtonEmoji = interaction.fields.getTextInputValue('product_buy_button_emoji_input');
            const newBuyButtonColor = interaction.fields.getTextInputValue('product_buy_button_style_input').toLowerCase().trim();

            // Mapear cores para números
            const colorMap = {
                'azul': 1,
                'blue': 1,
                'primary': 1,
                'cinza': 2,
                'cinzento': 2,
                'gray': 2,
                'grey': 2,
                'secondary': 2,
                'verde': 3,
                'green': 3,
                'success': 3,
                'vermelho': 4,
                'red': 4,
                'danger': 4,
                'link': 5,
                'premium': 6
            };

            const buttonStyle = colorMap[newBuyButtonColor];

            // Validar a cor do botão
            if (!buttonStyle) {
                await interaction.reply({
                    content: '❌ Cor inválida. Use: azul, cinza, verde, vermelho, link ou premium.',
                    ephemeral: true
                });
                return;
            }

            await this.handleUpdateProductBuyButtonConfig(interaction, productId, newBuyButtonText, newBuyButtonEmoji, buttonStyle, newBuyButtonColor);
        } else if (customId.startsWith('config_product_stock_add_modal_')) {
            const productId = customId.replace('config_product_stock_add_modal_', '');
            const stockItemsText = interaction.fields.getTextInputValue('product_stock_items_input');
            const infiniteStockText = interaction.fields.getTextInputValue('product_infinite_stock_input').toLowerCase().trim();

            // Validar entrada de estoque infinito
            const isInfinite = infiniteStockText === 'sim' || infiniteStockText === 's' || infiniteStockText === 'yes' || infiniteStockText === 'y';

            // Processar itens do estoque
            const stockItems = stockItemsText.split('\n').filter(item => item.trim() !== '').map(item => item.trim());

            if (stockItems.length === 0 && !isInfinite) {
                await interaction.reply({ content: '❌ Adicione pelo menos um item ao estoque ou configure como estoque infinito.', ephemeral: true });
                return;
            }

            await this.handleAddProductStockItems(interaction, productId, stockItems, isInfinite);
        } else if (customId.startsWith('config_product_cargo_modal_')) {
            const productId = customId.replace('config_product_cargo_modal_', '');
            const roleId = interaction.fields.getTextInputValue('product_cargo_input').trim();
            await this.handleUpdateProductConfigDetails(interaction, productId, 'roleId', roleId);
        } else if (customId.startsWith('config_product_import_modal_')) {
            const productId = customId.replace('config_product_import_modal_', '');
            const importCode = interaction.fields.getTextInputValue('product_import_code_input').trim().toUpperCase();
            await this.handleImportProductConfig(interaction, productId, importCode);
        } else if (customId.startsWith('confirm_refund_')) {
            const parts = customId.split('_');
            const buyerId = parts[2];
            const productId = parts[3];
            const reason = interaction.fields.getTextInputValue('refund_reason');
            const confirmation = interaction.fields.getTextInputValue('refund_confirm').toUpperCase();

            if (confirmation !== 'CONFIRMAR') {
                await interaction.reply({
                    content: '❌ **Reembolso cancelado!**\n\nVocê deve digitar exatamente "CONFIRMAR" para processar o reembolso.',
                    ephemeral: true
                });
                return;
            }

            await this.processRefund(interaction, buyerId, productId, reason);
        } else if (customId.startsWith('cart_quantity_modal_')) {
            const parts = customId.replace('cart_quantity_modal_', '').split('_');
            const productId = parts[0];
            const buyerId = parts[1];
            const newQuantity = parseInt(interaction.fields.getTextInputValue('cart_quantity_input'));

            if (isNaN(newQuantity) || newQuantity < 1) {
                await interaction.reply({ content: '❌ Quantidade inválida. Deve ser um número maior que 0.', ephemeral: true });
                return;
            }

            await this.handleUpdateCartQuantity(interaction, productId, buyerId, newQuantity);
        } else if (customId === 'criar_categoria_modal') {
            const categoryName = interaction.fields.getTextInputValue('category_name_input');

            // Sempre atualizar a mensagem original do painel
            await interaction.deferUpdate();

            // Validar nome da categoria
            if (!categoryName || categoryName.trim().length < 3) {
                await interaction.editReply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Nome de categoria inválido!')
                            .setDescription('O nome da categoria deve ter pelo menos 3 caracteres.')
                            .setColor(0xff0000)
                    ],
                    components: []
                });

                setTimeout(async () => {
                    await this.handleGerenciarCategorias(interaction);
                }, 2000);
                return;
            }

            // Obter dados do usuário
            const userData = database.getUser(userId) || {};

            // Inicializar array de categorias se não existir
            if (!userData.categories) {
                userData.categories = [];
            }

            // Verificar se já existe uma categoria com o mesmo nome
            const existingCategory = userData.categories.find(cat => cat.name.toLowerCase() === categoryName.toLowerCase());
            if (existingCategory) {
                await interaction.editReply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Categoria já existe!')
                            .setDescription(`Já existe uma categoria chamada "${categoryName}".`)
                            .setColor(0xff0000)
                    ],
                    components: []
                });

                setTimeout(async () => {
                    await this.handleGerenciarCategorias(interaction);
                }, 2000);
                return;
            }

            try {
                // Criar categoria no servidor do Discord
                const guild = interaction.guild;
                if (!guild) {
                    throw new Error('Não foi possível acessar o servidor.');
                }

                // Criar a categoria no Discord
                const discordCategory = await guild.channels.create({
                    name: categoryName,
                    type: 4, // 4 é o tipo para categoria
                    permissionOverwrites: [
                        {
                            id: guild.id, // @everyone
                            allow: ['ViewChannel'],
                            deny: []
                        }
                    ]
                });

                // Criar nova categoria com ID único
                const newCategory = {
                    id: Date.now().toString(), // ID único baseado no timestamp
                    name: categoryName,
                    createdAt: new Date().toISOString(),
                    discordCategoryId: discordCategory.id, // Salvar o ID da categoria do Discord
                    products: []
                };

                // Adicionar categoria aos dados do usuário
                userData.categories.push(newCategory);

                // Salvar dados atualizados
                database.saveUser(userId, userData);
            } catch (error) {
                console.error(`Erro ao criar categoria no Discord: ${error.message}`);

                await interaction.editReply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Erro ao criar categoria!')
                            .setDescription(`Ocorreu um erro ao criar a categoria no servidor: ${error.message}`)
                            .setColor(0xff0000)
                    ],
                    components: []
                });

                setTimeout(async () => {
                    await this.handleGerenciarCategorias(interaction);
                }, 2000);
                return;
            }

            // Mostrar mensagem de sucesso
            await interaction.editReply({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('✅ Categoria criada com sucesso!')
                        .setDescription(`A categoria "${categoryName}" foi criada com sucesso.`)
                        .setColor(0x00ff00)
                ],
                components: []
            });

            setTimeout(async () => {
                await this.handleGerenciarCategorias(interaction);
            }, 2000);
            return;
        } else if (customId === 'criar_produto_simples_modal') {
            // Extrair apenas o nome do produto
            const productName = interaction.fields.getTextInputValue('product_name_input');

            await interaction.deferUpdate();

            // Validar nome do produto
            if (!productName || productName.trim().length < 3) {
                await interaction.editReply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Nome de produto inválido!')
                            .setDescription('O nome do produto deve ter pelo menos 3 caracteres.')
                            .setColor(0xff0000)
                    ],
                    components: []
                });
                setTimeout(async () => {
                    await this.handleGerenciarProdutos(interaction);
                }, 2000);
                return;
            }

            const userData = database.getUser(userId) || {};

            // Verificar se existem categorias
            if (!userData.categories || userData.categories.length === 0) {
                await interaction.editReply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('⚠️ Nenhuma categoria encontrada!')
                            .setDescription('Você precisa criar uma categoria antes de adicionar produtos.')
                            .setColor(0xffa500)
                    ],
                    components: []
                });
                setTimeout(async () => {
                    await this.handleGerenciarProdutos(interaction);
                }, 2000);
                return;
            }

            // Salvar nome do produto temporariamente
            this.tempProductData = { productName };

            // Mostrar seleção de categoria
            const categoryOptions = userData.categories.map(cat => ({
                label: cat.name,
                value: cat.id,
            }));

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('select_category_for_simple_product')
                .setPlaceholder('Selecione uma categoria para o produto')
                .addOptions(categoryOptions);

            const selectRow = new ActionRowBuilder().addComponents(selectMenu);

            await interaction.editReply({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('📂 Selecionar Categoria')
                        .setDescription(`**Produto:** ${productName}\n\nSelecione a categoria onde este produto será criado:`)
                        .setColor(0x0099ff)
                ],
                components: [selectRow]
            });
        } else if (customId === 'criar_produto_completo_modal') {
            // Extrair todos os dados do modal
            const productName = interaction.fields.getTextInputValue('product_name_input');
            const productDescription = interaction.fields.getTextInputValue('product_description_input');
            const productPriceStr = interaction.fields.getTextInputValue('product_price_input');
            const productStockStr = interaction.fields.getTextInputValue('product_stock_input');
            const productImage = interaction.fields.getTextInputValue('product_image_input') || null;

            await interaction.deferUpdate();

            // Validações
            if (!productName || productName.trim().length < 3) {
                await interaction.editReply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Nome de produto inválido!')
                            .setDescription('O nome do produto deve ter pelo menos 3 caracteres.')
                            .setColor(0xff0000)
                    ],
                    components: []
                });
                setTimeout(async () => {
                    await this.handleGerenciarProdutos(interaction);
                }, 2000);
                return;
            }

            if (!productDescription || productDescription.trim().length < 10) {
                await interaction.editReply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Descrição inválida!')
                            .setDescription('A descrição do produto deve ter pelo menos 10 caracteres.')
                            .setColor(0xff0000)
                    ],
                    components: []
                });
                setTimeout(async () => {
                    await this.handleGerenciarProdutos(interaction);
                }, 2000);
                return;
            }

            // Validar preço
            const productPrice = parseFloat(productPriceStr.replace(',', '.'));
            if (isNaN(productPrice) || productPrice < 0) {
                await interaction.editReply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Preço inválido!')
                            .setDescription('O preço deve ser um número válido maior ou igual a 0.')
                            .setColor(0xff0000)
                    ],
                    components: []
                });
                setTimeout(async () => {
                    await this.handleGerenciarProdutos(interaction);
                }, 2000);
                return;
            }

            // Validar estoque
            const productStock = parseInt(productStockStr);
            if (isNaN(productStock) || productStock < 0) {
                await interaction.editReply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Estoque inválido!')
                            .setDescription('O estoque deve ser um número inteiro maior ou igual a 0.')
                            .setColor(0xff0000)
                    ],
                    components: []
                });
                setTimeout(async () => {
                    await this.handleGerenciarProdutos(interaction);
                }, 2000);
                return;
            }

            const userData = database.getUser(userId) || {};
            if (!userData.products) {
                userData.products = [];
            }

            const existingProduct = userData.products.find(prod => prod.name.toLowerCase() === productName.toLowerCase());
            if (existingProduct) {
                await interaction.editReply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Produto já existe!')
                            .setDescription(`Já existe um produto chamado "${productName}".`)
                            .setColor(0xff0000)
                    ],
                    components: []
                });
                setTimeout(async () => {
                    await this.handleGerenciarProdutos(interaction);
                }, 2000);
                return;
            }

            // Verificar se existem categorias
            if (!userData.categories || userData.categories.length === 0) {
                await interaction.editReply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('⚠️ Nenhuma categoria encontrada!')
                            .setDescription('Você precisa criar uma categoria antes de adicionar produtos.')
                            .setColor(0xffa500)
                    ],
                    components: []
                });
                setTimeout(async () => {
                    await this.handleGerenciarProdutos(interaction);
                }, 2000);
                return;
            }

            const categoryOptions = userData.categories.map(cat => ({
                label: cat.name,
                value: cat.id,
            }));

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('select_category_for_complete_product')
                .setPlaceholder('Selecione uma categoria')
                .addOptions(categoryOptions);

            const row = new ActionRowBuilder().addComponents(selectMenu);

            await interaction.editReply({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('🔗 Vincular Produto à Categoria')
                        .setDescription(`Selecione a categoria para o produto: **${productName}**\n\n📋 **Resumo do produto:**\n• **Nome:** ${productName}\n• **Preço:** R$ ${productPrice.toFixed(2).replace('.', ',')}\n• **Estoque:** ${productStock} unidades\n• **Descrição:** ${productDescription.substring(0, 100)}${productDescription.length > 100 ? '...' : ''}`)
                        .setColor(0x2196f3)
                ],
                components: [row]
            });

            // Armazenar todos os dados do produto temporariamente
            this.tempProductData = {
                userId,
                productName,
                productDescription,
                productPrice,
                productStock,
                productImage
            };
        } else if (customId === 'access_token_modal') {
            const accessToken = interaction.fields.getTextInputValue('access_token_input');

            // Sempre atualizar a mensagem original do painel, nunca criar nova reply
            await interaction.deferUpdate();

            // Validar formato do Access Token
            if (!accessToken.startsWith('APP_USR-') || accessToken.length < 50) {
                // Mostrar erro temporário na mesma mensagem
                await interaction.editReply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Formato do Access Token inválido!')
                            .setDescription(`**Requisitos:**\n• Deve começar com "APP_USR-"\n• Deve ter pelo menos 50 caracteres\n\n**Como obter o token correto:**\n1. Acesse: https://www.mercadopago.com.br/developers/panel\n2. Vá em "Suas integrações"\n3. Selecione sua aplicação\n4. Copie o **Access Token** (não o Public Key)`)
                            .setColor(0xff0000)
                    ],
                    components: []
                });
                setTimeout(async () => {
                    const { embed, components } = this.buildMercadoPagoConfigMessage(userId);
                    await interaction.editReply({ embeds: [embed], components });
                }, 2000);
                return;
            }

            // Validar credenciais com a API do Mercado Pago
            const validation = await this.validateMercadoPagoCredentials(accessToken);

            if (!validation.valid) {
                await interaction.editReply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Access Token inválido!')
                            .setDescription(validation.error)
                            .setColor(0xff0000)
                    ],
                    components: []
                });
                setTimeout(async () => {
                    const { embed, components } = this.buildMercadoPagoConfigMessage(userId);
                    await interaction.editReply({ embeds: [embed], components });
                }, 2000);
                return;
            }

            // Salvar Access Token e informações da conta
            this.saveUserCredential(userId, 'accessToken', accessToken, validation.accountInfo);
            console.log(`Access Token configurado para usuário ${userId}: ${accessToken.substring(0, 20)}...`);

            // Mostrar mensagem de sucesso temporária
            await interaction.editReply({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('✅ Access Token configurado com sucesso!')
                        .setColor(0x00ff00)
                ],
                components: []
            });
            setTimeout(async () => {
                const { embed, components } = this.buildMercadoPagoConfigMessage(userId);
                await interaction.editReply({ embeds: [embed], components });
            }, 1500);

        } else if (customId === 'client_secret_modal') {
            const clientSecret = interaction.fields.getTextInputValue('client_secret_input');

            // Sempre atualizar a mensagem original do painel, nunca criar nova reply
            await interaction.deferUpdate();

            // Validar formato do Client Secret
            // Client Secret pode ter diferentes formatos: APP_USR- ou formato alfanumérico de 32 caracteres
            const isValidClientSecret = (
                (clientSecret.startsWith('APP_USR-') && clientSecret.length >= 50) ||
                (/^[A-Za-z0-9]{32}$/.test(clientSecret))
            );

            if (!isValidClientSecret) {
                await interaction.editReply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Client Secret inválido!')
                            .setDescription('Deve começar com "APP_USR-" (mínimo 50 caracteres) ou ser um código alfanumérico de 32 caracteres.')
                            .setColor(0xff0000)
                    ],
                    components: []
                });
                setTimeout(async () => {
                    const { embed, components } = this.buildMercadoPagoConfigMessage(userId);
                    await interaction.editReply({ embeds: [embed], components });
                }, 2000);
                return;
            }

            // Salvar Client Secret
            this.saveUserCredential(userId, 'clientSecret', clientSecret);
            console.log(`Client Secret configurado para usuário ${userId}: ${clientSecret.substring(0, 20)}...`);

            // Mostrar mensagem de sucesso temporária
            await interaction.editReply({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('✅ Client Secret configurado com sucesso!')
                        .setColor(0x00ff00)
                ],
                components: []
            });
            setTimeout(async () => {
                const { embed, components } = this.buildMercadoPagoConfigMessage(userId);
                await interaction.editReply({ embeds: [embed], components });
            }, 1500);
        }
    }

    async handleConfirmMercadoPago(interaction) {
        const userId = interaction.user.id;
        const credentials = this.getUserCredentials(userId);

        if (!this.hasAllCredentials(userId)) {
            await interaction.reply({
                content: '❌ Você precisa configurar tanto o Access Token quanto o Client Secret antes de confirmar!',
                ephemeral: true
            });
            return;
        }

        // Sempre atualizar a mensagem original do painel, nunca criar nova reply
        await interaction.deferUpdate();

        const embed = new EmbedBuilder()
            .setTitle('✅ Mercado Pago Configurado')
            .setDescription(`Conta: ${credentials.accountInfo?.email || 'Não disponível'}\nCredenciais validadas com sucesso.`)
            .setColor(0x00ff00)
            .addFields(
                {
                    name: '💳 Pagamentos Disponíveis',
                    value: '• Cartão de crédito\n• PIX\n• Boleto',
                    inline: false
                }
            )
            .setFooter({ text: 'Configuração concluída' })
            .setTimestamp();

        // Marcar as credenciais como confirmadas
        const userData = database.getUser(userId) || {};
        userData.mercadoPagoConfirmed = true;
        userData.mercadoPagoConfirmedAt = new Date();
        database.saveUser(userId, userData);

        console.log(`✅ Configuração do Mercado Pago confirmada para usuário ${userId} (${credentials.accountInfo?.email})`);

        // Mostrar mensagem de sucesso temporária
        await interaction.editReply({ embeds: [embed], components: [] });
        setTimeout(async () => {
            const { embed, components } = this.buildMercadoPagoConfigMessage(userId);
            await interaction.editReply({ embeds: [embed], components });
        }, 2000);
    }

    async handleClearMercadoPagoCredentials(interaction) {
        const userId = interaction.user.id;
        const credentials = this.getUserCredentials(userId);

        // Verificar se há credenciais para limpar
        if (!credentials.accessToken && !credentials.clientSecret) {
            await interaction.reply({
                content: '❌ Não há credenciais do Mercado Pago configuradas para limpar!',
                ephemeral: true
            });
            return;
        }

        // Limpar imediatamente as credenciais sem confirmação
        await interaction.deferUpdate();

        // Limpar todas as credenciais do Mercado Pago
        const userData = database.getUser(userId) || {};
        delete userData.mercadoPagoAccessToken;
        delete userData.mercadoPagoClientSecret;
        delete userData.mercadoPagoAccountInfo;
        delete userData.mercadoPagoConfirmed;
        delete userData.mercadoPagoConfirmedAt;
        userData.updatedAt = new Date();
        database.saveUser(userId, userData);

        console.log(`🗑️ Credenciais do Mercado Pago limpas para usuário ${userId}`);

        // Atualizar a mensagem com a tela de configuração do Mercado Pago
        const { embed, components } = this.buildMercadoPagoConfigMessage(userId);

        await interaction.editReply({
            embeds: [embed],
            components: components
        });
    }



    async stop() {
        try {
            if (this.client) {
                this.isActive = false;
                this.client.destroy();
                this.client = null;
                console.log(`Bot do usuário ${this.userId} foi desconectado`);
            }
        } catch (error) {
            console.error(`Erro ao parar bot do usuário ${this.userId}:`, error);
            this.isActive = false;
            this.client = null;
        }
    }

    getStatus() {
        return {
            userId: this.userId,
            isActive: this.isActive,
            clientId: this.clientId,
            botTag: this.client?.user?.tag || 'Desconectado'
        };
    }

    // Métodos para gerenciar credenciais do Mercado Pago
    async validateMercadoPagoCredentials(accessToken) {
        try {
            console.log(`🔍 Validando Access Token: ${accessToken.substring(0, 20)}...`);

            // Usar endpoint /users/me que é válido e retorna informações do usuário
            const response = await axios.get('https://api.mercadopago.com/users/me', {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000 // 10 segundos timeout
            });

            console.log(`✅ Resposta da API MP: Status ${response.status}`);

            if (response.status === 200 && response.data) {
                return {
                    valid: true,
                    accountInfo: {
                        email: response.data.email || 'Email não disponível',
                        nickname: response.data.nickname || response.data.first_name || 'Nome não disponível',
                        country: response.data.country_id || 'País não disponível',
                        id: response.data.id || 'ID não disponível'
                    }
                };
            }
            return { valid: false, error: 'Resposta inválida da API' };
        } catch (error) {
            console.error('❌ Erro ao validar credenciais do Mercado Pago:', {
                message: error.message,
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data
            });

            // Mensagens de erro mais específicas
            if (error.response?.status === 401) {
                return {
                    valid: false,
                    error: '**Access Token inválido ou expirado!**\n\n**Possíveis causas:**\n• Token copiado incorretamente\n• Token expirado\n• Usando Public Key ao invés do Access Token\n• Espaços extras no início/fim do token\n\n**Solução:**\n1. Acesse: https://www.mercadopago.com.br/developers/panel\n2. Vá em "Suas integrações"\n3. Selecione sua aplicação\n4. Copie o **Access Token** (APP_USR-...)\n5. Cole exatamente como está, sem espaços extras'
                };
            } else if (error.response?.status === 404) {
                return {
                    valid: false,
                    error: '**Endpoint não encontrado!**\n\nO Access Token parece estar correto, mas houve um problema com a API do Mercado Pago. Isso pode ser temporário.\n\n**Tente novamente em alguns minutos.**'
                };
            } else if (error.response?.status === 403) {
                return {
                    valid: false,
                    error: 'Access Token sem permissões necessárias. Verifique se tem acesso à API.'
                };
            } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
                return {
                    valid: false,
                    error: 'Erro de conexão com a API do Mercado Pago. Tente novamente em alguns minutos.'
                };
            } else if (error.code === 'ECONNABORTED') {
                return {
                    valid: false,
                    error: 'Timeout na validação. Tente novamente.'
                };
            }

            return {
                valid: false,
                error: error.response?.data?.message || `Erro na validação: ${error.message}`
            };
        }
    }

    getUserCredentials(userId) {
        const userData = database.getUser(userId) || {};
        return {
            accessToken: userData.mercadoPagoAccessToken || null,
            clientSecret: userData.mercadoPagoClientSecret || null,
            accountInfo: userData.mercadoPagoAccountInfo || null
        };
    }

    saveUserCredential(userId, credentialType, value, accountInfo = null) {
        const userData = database.getUser(userId) || {};

        if (credentialType === 'accessToken') {
            userData.mercadoPagoAccessToken = value;
            if (accountInfo) {
                userData.mercadoPagoAccountInfo = accountInfo;
            }
        } else if (credentialType === 'clientSecret') {
            userData.mercadoPagoClientSecret = value;
        }

        userData.updatedAt = new Date();
        database.saveUser(userId, userData);
    }

    hasAllCredentials(userId) {
        const credentials = this.getUserCredentials(userId);
        return credentials.accessToken && credentials.clientSecret;
    }



    // Função para construir a mensagem de configuração do Mercado Pago
    buildMercadoPagoConfigMessage(userId) {
        const credentials = this.getUserCredentials(userId);

        // Status das credenciais com emojis mais informativos (removidas variáveis não utilizadas)

        // Verificar se todas as credenciais estão configuradas
        const allConfigured = this.hasAllCredentials(userId);

        const embed = new EmbedBuilder()
            .setTitle('💳 Configuração Mercado Pago')
            .setDescription(allConfigured ?
                          '**✅ Configuração Completa!**\nSuas credenciais estão prontas para uso.' :
                          '**⚙️ Configure suas credenciais**\nPara começar a receber pagamentos automáticos.')
            .setColor(allConfigured ? 0x00ff00 : 0x5865F2)
            .addFields(
                {
                    name: '🔑 Status das Credenciais',
                    value: `**Access Token:** ${credentials.accessToken ? '✅ Configurado' : '⚠️ Pendente'}\n**Client Secret:** ${credentials.clientSecret ? '✅ Configurado' : '⚠️ Pendente'}`,
                    inline: false
                },
                allConfigured ? {
                    name: '📊 Sistema',
                    value: '🟢 **Pronto para receber pagamentos**\nTodas as credenciais foram validadas',
                    inline: false
                } : {
                    name: '📊 Próximo Passo',
                    value: '🟡 **Configure ambas as credenciais**\nAccess Token e Client Secret são obrigatórios',
                    inline: false
                }
            )
            .setFooter({
                text: allConfigured ?
                      'Clique em "Confirmar" para ativar os pagamentos' :
                      'Configure Access Token e Client Secret para continuar'
            })
            .setTimestamp();

        // Adicionar informações da conta apenas se estiver configurada (ocultas com spoiler)
        if (credentials.accountInfo) {
            embed.addFields({
                name: '👤 Conta Vinculada',
                value: `📧 ||${credentials.accountInfo.email}||\n🆔 ||${credentials.accountInfo.nickname}||`,
                inline: false
            });
        }

        const components = [];

                // Linha 1: Configuração das credenciais
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_access_token')
                    .setLabel('Configurar Acess Token')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔑'),
                new ButtonBuilder()
                    .setCustomId('setup_client_secret')
                    .setLabel('Configurar Client Secret')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔐'),
                new ButtonBuilder()
                    .setCustomId('test_mercado_pago_connection')
                    .setLabel('Testar Conexão')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🧪')
            );
        components.push(row1);

        // Linha 2: Navegação e Ajuda
        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setLabel('Painel Mercado Pago')
                    .setStyle(ButtonStyle.Link)
                    .setURL('https://www.mercadopago.com.br/developers/panel')
                    .setEmoji('🔗'),
                new ButtonBuilder()
                    .setCustomId('mercado_pago_help')
                    .setLabel('Ajuda')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('❓'),
                new ButtonBuilder()
                    .setCustomId('back_to_auth')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );
        components.push(row2);

        // Linha 3: Ações principais e destrutivas (Confirmar e Excluir)
        if (credentials.accessToken || credentials.clientSecret) {
            const row3 = new ActionRowBuilder();
            
            if (allConfigured) {
                row3.addComponents(
                    new ButtonBuilder()
                        .setCustomId('confirm_mercado_pago')
                        .setLabel('Confirmar')
                        .setStyle(ButtonStyle.Success)
                        .setEmoji('✅')
                );
            }
            
            row3.addComponents(
                new ButtonBuilder()
                    .setCustomId('clear_mercado_pago_credentials')
                    .setLabel('Excluir Credenciais')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🗑️')
            );
            
            components.push(row3);
        } else if (allConfigured) {
            // Se não há credenciais para excluir mas ainda precisa do botão confirmar
            const row3 = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('confirm_mercado_pago')
                        .setLabel('Confirmar')
                        .setStyle(ButtonStyle.Success)
                        .setEmoji('✅')
                );
            components.push(row3);
        }

        return { embed, components };
    }

    // Função para atualizar dinamicamente a mensagem de configuração do Mercado Pago
    async updateMercadoPagoConfigMessage(interaction, userId) {
        // Usar a função de construção para obter embed e componentes
        const { embed, components } = this.buildMercadoPagoConfigMessage(userId);

        // Tentar atualizar painel dinâmico primeiro
        const updated = await this.updateDynamicPanel(userId, embed, components);

        if (updated) {
            // Se conseguiu atualizar o painel dinâmico, confirmar a interação
            if (interaction.isButton() && !interaction.replied && !interaction.deferred) {
                await interaction.deferUpdate();
            }
            return;
        }

        // Se não conseguiu usar painel dinâmico, usar lógica tradicional
        try {
            // Verificar o estado da interação e usar o método apropriado
            if (interaction.replied || interaction.deferred) {
                // Se já foi respondida ou deferida, usar editReply
                await interaction.editReply({ embeds: [embed], components });
            } else if (interaction.isButton() || interaction.isStringSelectMenu()) {
                // Para interações de componentes (botões/select), usar update
                await interaction.update({ embeds: [embed], components });
            } else {
                // Para comandos slash ou outras interações, usar reply
                await interaction.reply({ embeds: [embed], components });
            }
        } catch (error) {
            console.error('Erro ao atualizar mensagem do Mercado Pago:', error.message);

            // Log detalhado para debug
            console.log('Estado da interação:', {
                replied: interaction.replied,
                deferred: interaction.deferred,
                isButton: interaction.isButton?.(),
                isCommand: interaction.isChatInputCommand?.(),
                customId: interaction.customId || 'N/A'
            });

            throw new Error(`Falha ao atualizar interface: ${error.message}`);
        }
    }

    // ===== NOVOS HANDLERS PARA CONFIGURAÇÃO =====

    async handleAuthHelp(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('❓ Central de Ajuda - Configuração de Pagamentos')
            .setDescription('**Precisa de ajuda para configurar seus métodos de pagamento?**\n\n' +
                          'Estamos aqui para te ajudar a começar a vender o mais rápido possível!')
            .setColor(0x5865F2)
            .addFields(
                {
                    name: '🤔 Qual método escolher?',
                    value: '**Mercado Pago:** Ideal se você quer aceitar cartões, PIX e boleto\n' +
                           '**PIX Nodex Pay:** Perfeito para pagamentos rápidos apenas com PIX\n\n' +
                           '💡 *Recomendamos configurar ambos para máxima conversão!*',
                    inline: false
                },
                {
                    name: '🔧 Dificuldades técnicas?',
                    value: '• **Não sei onde encontrar as credenciais**\n' +
                           '• **Erro ao configurar Access Token**\n' +
                           '• **Problemas com validação da conta**\n' +
                           '• **Dúvidas sobre taxas e prazos**',
                    inline: true
                },
                {
                    name: '📞 Como obter suporte?',
                    value: '• **Discord:** Abra um ticket\n' +
                           '• **Email:** <EMAIL>\n' +
                           '• **WhatsApp:** +55 11 99999-9999\n' +
                           '• **Documentação:** [docs.nodexsolutions.com](https://docs.nodexsolutions.com)',
                    inline: true
                },
                {
                    name: '⚡ Suporte Prioritário',
                    value: '🎯 **Configuração Assistida:** Nossa equipe pode configurar tudo para você!\n' +
                           '⏰ **Disponível:** Segunda a Sexta, 9h às 18h\n' +
                           '🚀 **Tempo médio:** 15 minutos para configuração completa',
                    inline: false
                }
            )
            .setFooter({ text: 'Powered by: Nodex Solutions | Estamos aqui para ajudar!' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('contact_priority_support')
                    .setLabel('Suporte Prioritário')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('🚀'),
                new ButtonBuilder()
                    .setCustomId('view_documentation')
                    .setLabel('Ver Documentação')
                    .setStyle(ButtonStyle.Link)
                    .setURL('https://docs.nodexsolutions.com/pagamentos')
                    .setEmoji('📚'),
                new ButtonBuilder()
                    .setCustomId('back_to_auth')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }

    async handleMercadoPagoHelp(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('📋 Como Obter Credenciais do Mercado Pago')
            .setDescription('**Siga este guia passo a passo para configurar suas credenciais:**')
            .setColor(0x5865F2)
            .addFields(
                {
                    name: '1️⃣ Acesse o Painel do Mercado Pago',
                    value: '• Vá para: [developers.mercadopago.com.br](https://www.mercadopago.com.br/developers/panel)\n• Faça login com sua conta do Mercado Pago',
                    inline: false
                },
                {
                    name: '2️⃣ Navegue até "Suas integrações"',
                    value: '• No menu lateral, clique em **"Suas integrações"**\n• Você verá uma lista de aplicações existentes',
                    inline: false
                },
                {
                    name: '3️⃣ Crie ou Selecione uma Aplicação',
                    value: '• **Nova aplicação:** Clique em "Criar aplicação"\n• **Aplicação existente:** Clique na aplicação desejada',
                    inline: false
                },
                {
                    name: '4️⃣ Copie as Credenciais',
                    value: '• **Access Token:** Copie o token que começa com `APP_USR-`\n• **Client Secret:** Copie o código secreto da aplicação\n• ⚠️ **Importante:** Use as credenciais de **Produção** para vendas reais',
                    inline: false
                },
                {
                    name: '5️⃣ Configure no Bot',
                    value: '• Volte para esta tela\n• Clique em "Configurar Access Token" e cole o token\n• Clique em "Configurar Client Secret" e cole o código\n• Clique em "Confirmar" para ativar',
                    inline: false
                }
            )
            .setFooter({ text: 'Precisa de ajuda? Entre em contato com nosso suporte!' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setLabel('Abrir Painel Mercado Pago')
                    .setStyle(ButtonStyle.Link)
                    .setURL('https://www.mercadopago.com.br/developers/panel')
                    .setEmoji('🔗'),
                new ButtonBuilder()
                    .setCustomId('contact_support_mp')
                    .setLabel('Contatar Suporte')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🆘'),
                new ButtonBuilder()
                    .setCustomId('back_to_mercado_pago_config')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }

    async handleTestMercadoPagoConnection(interaction) {
        const userId = interaction.user.id;
        const credentials = this.getUserCredentials(userId);

        // Mostrar indicador de teste
        await interaction.deferUpdate();

        const embed = new EmbedBuilder()
            .setTitle('🧪 Testando Conexão com Mercado Pago')
            .setDescription('**Verificando suas credenciais...**\n\n⏳ *Aguarde enquanto validamos sua configuração*')
            .setColor(0xffa500)
            .addFields(
                {
                    name: '🔍 Verificações em andamento:',
                    value: '• Validando Access Token...\n• Verificando Client Secret...\n• Testando conexão com API...\n• Confirmando permissões...',
                    inline: false
                }
            )
            .setFooter({ text: 'Este teste pode levar alguns segundos...' })
            .setTimestamp();

        await interaction.editReply({ embeds: [embed], components: [] });

        // Simular teste (aqui você implementaria a validação real)
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Resultado do teste
        const testSuccess = credentials.accessToken && credentials.clientSecret;

        const resultEmbed = new EmbedBuilder()
            .setTitle(testSuccess ? '✅ Teste de Conexão - Sucesso!' : '❌ Teste de Conexão - Falhou')
            .setDescription(testSuccess ?
                          '**Parabéns! Suas credenciais estão funcionando perfeitamente.**\n\n🎉 Seu bot está pronto para receber pagamentos!' :
                          '**Encontramos alguns problemas com suas credenciais.**\n\n⚠️ Verifique se as credenciais estão corretas.')
            .setColor(testSuccess ? 0x00ff00 : 0xff0000)
            .addFields(
                {
                    name: testSuccess ? '✅ Tudo funcionando:' : '❌ Problemas encontrados:',
                    value: testSuccess ?
                           '• Access Token válido\n• Client Secret válido\n• Conexão com API estabelecida\n• Permissões confirmadas' :
                           '• Verifique o Access Token\n• Confirme o Client Secret\n• Teste sua conexão de internet\n• Contate o suporte se persistir',
                    inline: false
                }
            )
            .setFooter({ text: testSuccess ? 'Sistema pronto para vendas!' : 'Precisa de ajuda? Entre em contato conosco!' })
            .setTimestamp();

        const resultRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('back_to_mercado_pago_config')
                    .setLabel('Voltar à Configuração')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⬅️')
            );

        if (!testSuccess) {
            resultRow.addComponents(
                new ButtonBuilder()
                    .setCustomId('contact_support_mp')
                    .setLabel('Contatar Suporte')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🆘')
            );
        }

        await interaction.editReply({ embeds: [resultEmbed], components: [resultRow] });
    }

    async handleAuthPix(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('💳 Autenticação com PIX')
            .setDescription('Configure sua chave PIX para receber pagamentos via Nodex Pay:')
            .setColor(0x00ff88)
            .addFields(
                {
                    name: '🏦 Nodex Pay (PIX)',
                    value: 'Configure sua chave PIX para receber pagamentos instantâneos via Nodex Pay',
                    inline: false
                },
                {
                    name: '📋 Informações necessárias:',
                    value: '• **Chave PIX** (CPF, CNPJ, Email ou Telefone)\n• **Nome do titular da conta**\n• **Banco da conta**',
                    inline: false
                },
                {
                    name: '⚡ Vantagens do PIX:',
                    value: '• Pagamentos instantâneos 24/7\n• Sem taxas para o cliente\n• Confirmação automática\n• Maior conversão de vendas',
                    inline: false
                }
            )
            .setFooter({ text: 'Clique em "Configurar PIX" para inserir sua chave' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_pix_key')
                    .setLabel('Configurar Chave PIX')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🔑'),
                new ButtonBuilder()
                    .setCustomId('back_to_config')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }

    async handleAlterarBotaoPagamento(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('✏️ Alterar Botão de Pagamento')
            .setDescription('Personalize o texto que aparece no botão de pagamento dos seus produtos:')
            .setColor(0xffa500)
            .addFields(
                {
                    name: '🎨 Personalização:',
                    value: 'Você pode alterar o texto do botão de pagamento para algo mais atrativo ou personalizado para sua marca.',
                    inline: false
                },
                {
                    name: '💡 Exemplos de textos:',
                    value: '• "Comprar Agora"\n• "Adquirir Produto"\n• "Finalizar Compra"\n• "Pagar com PIX"\n• "Garantir Meu Acesso"',
                    inline: false
                },
                {
                    name: '📝 Texto atual:',
                    value: '`Finalizar Compra` (padrão)',
                    inline: false
                }
            )
            .setFooter({ text: 'Clique em "Alterar Texto" para personalizar' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('change_button_text')
                    .setLabel('Alterar Texto do Botão')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('✏️'),
                new ButtonBuilder()
                    .setCustomId('back_to_config')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }

    async handleAdicionarCategoria(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('➕ Adicionar Categoria')
            .setDescription('Crie uma nova categoria para organizar seus produtos:')
            .setColor(0x57f287)
            .addFields(
                {
                    name: '📂 Organização:',
                    value: 'As categorias ajudam a organizar seus produtos de forma clara e profissional.',
                    inline: false
                },
                {
                    name: '💡 Exemplos de categorias:',
                    value: '• "Produtos Digitais"\n• "Cursos Online"\n• "E-books"\n• "Software"\n• "Consultoria"\n• "Assinaturas"',
                    inline: false
                },
                {
                    name: '📋 Informações da categoria:',
                    value: '• Nome da categoria\n• Descrição (opcional)\n• Emoji para identificação',
                    inline: false
                }
            )
            .setFooter({ text: 'Clique em "Criar Categoria" para começar' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('create_category')
                    .setLabel('Criar Nova Categoria')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('➕'),
                new ButtonBuilder()
                    .setCustomId('list_categories')
                    .setLabel('Ver Categorias Existentes')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('📋'),
                new ButtonBuilder()
                    .setCustomId('back_to_config')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }
    
    async handleGerenciarCategorias(interaction) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const categories = userData.categories || [];
        
        const embed = new EmbedBuilder()
            .setTitle('📂 Gerenciar Categorias')
            .setDescription('Gerencie as categorias de produtos da sua loja virtual.')
            .setColor(0x5865F2)
            .addFields(
                {
                    name: '📋 Sobre as categorias:',
                    value: 'As categorias ajudam a organizar seus produtos de forma clara e profissional.',
                    inline: false
                },
                {
                    name: '📊 Suas categorias:',
                    value: categories.length > 0 
                        ? categories.map(cat => `• ${cat.name} (${cat.products?.length || 0} produtos)`).join('\n')
                        : 'Você ainda não criou nenhuma categoria.',
                    inline: false
                },
                {
                    name: '💡 Exemplos de categorias:',
                    value: '• Cursos\n• E-books\n• Serviços\n• Assinaturas\n• Produtos digitais',
                    inline: false
                }
            )
            .setFooter({ text: 'Clique em "Criar Nova Categoria" para começar' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('criar_nova_categoria')
                    .setLabel('Criar Nova Categoria')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('➕'),
                new ButtonBuilder()
                    .setCustomId('remover_categoria')
                    .setLabel('Remover Categoria')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🗑️')
                    .setDisabled(categories.length === 0), // Desabilita se não houver categorias
                new ButtonBuilder()
                    .setCustomId('back_to_config')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        // Verificar se a interação já foi respondida (caso de chamada após modal)
        if (interaction.replied || interaction.deferred) {
            await interaction.editReply({ embeds: [embed], components: [row] });
        } else {
            await interaction.update({ embeds: [embed], components: [row] });
        }
    }
    
    async handleRemoverCategoria(interaction) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const categories = userData.categories || [];
        
        // Verificar se existem categorias para remover
        if (categories.length === 0) {
            const embed = new EmbedBuilder()
                .setTitle('❌ Nenhuma categoria encontrada')
                .setDescription('Você não possui categorias para remover.')
                .setColor(0xff0000);
                
            await interaction.update({ embeds: [embed], components: [] });
            
            setTimeout(async () => {
                await this.handleGerenciarCategorias(interaction);
            }, 2000);
            return;
        }
        
        // Criar embed para seleção de categoria
        const embed = new EmbedBuilder()
            .setTitle('🗑️ Remover Categoria')
            .setDescription('Selecione a categoria que deseja remover:')
            .setColor(0xff0000)
            .addFields(
                {
                    name: '⚠️ Atenção:',
                    value: 'A remoção de uma categoria excluirá também todos os produtos associados a ela e a categoria no Discord. Esta ação não pode ser desfeita!',
                    inline: false
                }
            )
            .setFooter({ text: 'Selecione uma categoria na lista abaixo' })
            .setTimestamp();
        
        // Criar select menu com as categorias disponíveis
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('select_categoria_remover')
            .setPlaceholder('Selecione uma categoria para remover')
            .addOptions(
                categories.map(category => ({
                    label: category.name,
                    description: `${category.products?.length || 0} produtos`,
                    value: category.id
                }))
            );
        
        const selectRow = new ActionRowBuilder().addComponents(selectMenu);
        
        // Botão para voltar
        const buttonRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('cancelar_remover_categoria')
                    .setLabel('Cancelar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );
        
        await interaction.update({ embeds: [embed], components: [selectRow, buttonRow] });
    }
    
    async handleCriarProduto(interaction) {
        const modal = new ModalBuilder()
            .setCustomId('criar_produto_simples_modal')
            .setTitle('Criar Novo Produto');

        // Apenas nome do produto
        const productNameInput = new TextInputBuilder()
            .setCustomId('product_name_input')
            .setLabel('Nome do Produto')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Ex: Ebook de Marketing Digital')
            .setMinLength(3)
            .setMaxLength(100)
            .setRequired(true);

        const row1 = new ActionRowBuilder().addComponents(productNameInput);
        modal.addComponents(row1);

        await interaction.showModal(modal);
    }

    async handleEditarProduto(interaction) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const products = userData.products || [];

        // Verificar se existem produtos para editar
        if (products.length === 0) {
            const embed = new EmbedBuilder()
                .setTitle('❌ Nenhum produto encontrado')
                .setDescription('Você não possui produtos para editar. Crie um produto primeiro!')
                .setColor(0xff0000);

            await interaction.update({ embeds: [embed], components: [] });

            setTimeout(async () => {
                await this.handleGerenciarProdutos(interaction);
            }, 2000);
            return;
        }

        // Criar opções do select menu com os produtos
        const productOptions = products.map(product => ({
            label: product.name.length > 100 ? product.name.substring(0, 97) + '...' : product.name,
            value: product.id,
            description: `R$ ${product.details.price.toFixed(2).replace('.', ',')} - Estoque: ${product.details.stock}`,
            emoji: product.details.stock > 0 ? '📦' : '❌'
        }));

        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('select_product_to_edit')
            .setPlaceholder('Selecione um produto para editar')
            .addOptions(productOptions);

        const row = new ActionRowBuilder().addComponents(selectMenu);

        const embed = new EmbedBuilder()
            .setTitle('✏️ Editar Produto')
            .setDescription(`Selecione o produto que deseja editar:\n\n📊 **Seus produtos (${products.length}):**`)
            .setColor(0x2196f3)
            .addFields(
                {
                    name: '📝 O que você pode editar:',
                    value: '• Nome do produto\n• Descrição\n• Preço\n• Estoque\n• Imagem\n• Categoria',
                    inline: false
                }
            )
            .setFooter({ text: 'Selecione um produto da lista abaixo' })
            .setTimestamp();

        await interaction.update({ embeds: [embed], components: [row] });
    }

    async handleRemoverProduto(interaction) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const products = userData.products || [];

        // Verificar se existem produtos para remover
        if (products.length === 0) {
            const embed = new EmbedBuilder()
                .setTitle('❌ Nenhum produto encontrado')
                .setDescription('Você não possui produtos para remover.')
                .setColor(0xff0000);

            await interaction.update({ embeds: [embed], components: [] });

            setTimeout(async () => {
                await this.handleGerenciarProdutos(interaction);
            }, 2000);
            return;
        }

        // Criar opções do select menu com os produtos
        const productOptions = products.map(product => ({
            label: product.name.length > 100 ? product.name.substring(0, 97) + '...' : product.name,
            value: product.id,
            description: `R$ ${product.details.price.toFixed(2).replace('.', ',')} - Estoque: ${product.details.stock}`,
            emoji: '🗑️'
        }));

        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('select_product_to_remove')
            .setPlaceholder('Selecione um produto para remover')
            .addOptions(productOptions);

        const row = new ActionRowBuilder().addComponents(selectMenu);

        const embed = new EmbedBuilder()
            .setTitle('🗑️ Remover Produto')
            .setDescription(`Selecione o produto que deseja remover:\n\n📊 **Seus produtos (${products.length}):**`)
            .setColor(0xff0000)
            .addFields(
                {
                    name: '⚠️ Atenção:',
                    value: 'Esta ação irá remover permanentemente o produto, incluindo:\n• Canal do Discord\n• Todas as configurações\n• Histórico de vendas\n\n**Esta ação não pode ser desfeita!**',
                    inline: false
                }
            )
            .setFooter({ text: 'Selecione um produto da lista abaixo' })
            .setTimestamp();

        await interaction.update({ embeds: [embed], components: [row] });
    }

    async handleEditProductName(interaction, productId) {
        const modal = new ModalBuilder()
            .setCustomId(`edit_product_name_modal_${productId}`)
            .setTitle('Editar Nome do Produto');

        const nameInput = new TextInputBuilder()
            .setCustomId('product_name_input')
            .setLabel('Novo Nome do Produto')
            .setStyle(TextInputStyle.Short)
            .setRequired(true);

        modal.addComponents(new ActionRowBuilder().addComponents(nameInput));
        await interaction.showModal(modal);
    }

    async handleEditProductDescription(interaction, productId) {
        const modal = new ModalBuilder()
            .setCustomId(`edit_product_description_modal_${productId}`)
            .setTitle('Editar Descrição do Produto');

        const descriptionInput = new TextInputBuilder()
            .setCustomId('product_description_input')
            .setLabel('Nova Descrição do Produto')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Descreva detalhadamente o que o produto oferece...')
            .setMinLength(10)
            .setMaxLength(1000)
            .setRequired(true);

        modal.addComponents(new ActionRowBuilder().addComponents(descriptionInput));
        await interaction.showModal(modal);
    }

    async handleEditProductImage(interaction, productId) {
        const modal = new ModalBuilder()
            .setCustomId(`edit_product_image_modal_${productId}`)
            .setTitle('Editar Imagem do Produto');

        const imageInput = new TextInputBuilder()
            .setCustomId('product_image_input')
            .setLabel('URL da Nova Imagem')
            .setStyle(TextInputStyle.Short)
            .setRequired(true);

        modal.addComponents(new ActionRowBuilder().addComponents(imageInput));
        await interaction.showModal(modal);
    }

    async handleEditProductPrice(interaction, productId) {
        const modal = new ModalBuilder()
            .setCustomId(`edit_product_price_modal_${productId}`)
            .setTitle('Editar Preço do Produto');

        const priceInput = new TextInputBuilder()
            .setCustomId('product_price_input')
            .setLabel('Novo Preço (ex: 99.99)')
            .setStyle(TextInputStyle.Short)
            .setRequired(true);

        modal.addComponents(new ActionRowBuilder().addComponents(priceInput));
        await interaction.showModal(modal);
    }

    async handleEditProductStock(interaction, productId) {
        const modal = new ModalBuilder()
            .setCustomId(`edit_product_stock_modal_${productId}`)
            .setTitle('Editar Estoque do Produto');

        const stockInput = new TextInputBuilder()
            .setCustomId('product_stock_input')
            .setLabel('Novo Estoque')
            .setStyle(TextInputStyle.Short)
            .setRequired(true);

        modal.addComponents(new ActionRowBuilder().addComponents(stockInput));
        await interaction.showModal(modal);
    }

    // ===== HANDLERS DE CONFIGURAÇÃO DE PRODUTO =====

    async handleConfigProductName(interaction, productId) {
        // Verificar se a interação ainda é válida
        if (interaction.replied || interaction.deferred) {
            console.log('Interação já foi respondida ou deferida');
            return;
        }

        try {
            const modal = new ModalBuilder()
                .setCustomId(`config_product_name_modal_${productId}`)
                .setTitle('Configurar Nome do Produto');

            const nameInput = new TextInputBuilder()
                .setCustomId('product_name_input')
                .setLabel('Nome do Produto')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('Ex: Ebook de Marketing Digital')
                .setMinLength(3)
                .setMaxLength(100)
                .setRequired(true);

            modal.addComponents(new ActionRowBuilder().addComponents(nameInput));
            await interaction.showModal(modal);
        } catch (error) {
            console.error('Erro ao mostrar modal de nome:', error.message);
        }
    }

    async handleConfigProductDescription(interaction, productId) {
        if (interaction.replied || interaction.deferred) {
            console.log('Interação já foi respondida ou deferida');
            return;
        }

        try {
            const modal = new ModalBuilder()
                .setCustomId(`config_product_description_modal_${productId}`)
                .setTitle('Configurar Descrição do Produto');

            const descriptionInput = new TextInputBuilder()
                .setCustomId('product_description_input')
                .setLabel('Descrição do Produto')
                .setStyle(TextInputStyle.Paragraph)
                .setPlaceholder('Descreva detalhadamente o que o produto oferece...')
                .setMinLength(10)
                .setMaxLength(1000)
                .setRequired(true);

            modal.addComponents(new ActionRowBuilder().addComponents(descriptionInput));
            await interaction.showModal(modal);
        } catch (error) {
            console.error('Erro ao mostrar modal de descrição:', error.message);
        }
    }

    async handleConfigProductPrice(interaction, productId) {
        if (interaction.replied || interaction.deferred) {
            console.log('Interação já foi respondida ou deferida');
            return;
        }

        try {
            const modal = new ModalBuilder()
                .setCustomId(`config_product_price_modal_${productId}`)
                .setTitle('Configurar Preço do Produto');

            const priceInput = new TextInputBuilder()
                .setCustomId('product_price_input')
                .setLabel('Preço (R$)')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('Ex: 29.90')
                .setRequired(true);

            modal.addComponents(new ActionRowBuilder().addComponents(priceInput));
            await interaction.showModal(modal);
        } catch (error) {
            console.error('Erro ao mostrar modal de preço:', error.message);
        }
    }

    async handleConfigProductThumbnail(interaction, productId) {
        if (interaction.replied || interaction.deferred) {
            console.log('Interação já foi respondida ou deferida');
            return;
        }

        try {
            const modal = new ModalBuilder()
                .setCustomId(`config_product_thumbnail_modal_${productId}`)
                .setTitle('Configurar Miniatura do Produto');

            const thumbnailInput = new TextInputBuilder()
                .setCustomId('product_thumbnail_input')
                .setLabel('URL da Miniatura')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('https://exemplo.com/miniatura.png')
                .setRequired(true);

            modal.addComponents(new ActionRowBuilder().addComponents(thumbnailInput));
            await interaction.showModal(modal);
        } catch (error) {
            console.error('Erro ao mostrar modal de miniatura:', error.message);
        }
    }

    async handleConfigProductBanner(interaction, productId) {
        if (interaction.replied || interaction.deferred) {
            console.log('Interação já foi respondida ou deferida');
            return;
        }

        try {
            const modal = new ModalBuilder()
                .setCustomId(`config_product_banner_modal_${productId}`)
                .setTitle('Configurar Banner do Produto');

            const bannerInput = new TextInputBuilder()
                .setCustomId('product_banner_input')
                .setLabel('URL do Banner')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('https://exemplo.com/banner.png')
                .setRequired(true);

            modal.addComponents(new ActionRowBuilder().addComponents(bannerInput));
            await interaction.showModal(modal);
        } catch (error) {
            console.error('Erro ao mostrar modal de banner:', error.message);
        }
    }

    async handleConfigProductStockAdd(interaction, productId) {
        if (interaction.replied || interaction.deferred) {
            console.log('Interação já foi respondida ou deferida');
            return;
        }

        try {
            const modal = new ModalBuilder()
                .setCustomId(`config_product_stock_add_modal_${productId}`)
                .setTitle('Adicionar Estoque');

            const stockItemsInput = new TextInputBuilder()
                .setCustomId('product_stock_items_input')
                .setLabel('Itens do Estoque (um por linha)')
                .setStyle(TextInputStyle.Paragraph)
                .setPlaceholder('<EMAIL>:senha123\<EMAIL>:senha456\nchave-produto-001\nchave-produto-002')
                .setRequired(true);

            const infiniteStockInput = new TextInputBuilder()
                .setCustomId('product_infinite_stock_input')
                .setLabel('Estoque Infinito? (sim/não)')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('sim ou não')
                .setValue('não')
                .setRequired(true);

            modal.addComponents(
                new ActionRowBuilder().addComponents(stockItemsInput),
                new ActionRowBuilder().addComponents(infiniteStockInput)
            );
            await interaction.showModal(modal);
        } catch (error) {
            console.error('Erro ao mostrar modal de estoque:', error.message);
        }
    }

    async handleConfigProductStockClear(interaction, productId) {
        if (interaction.replied || interaction.deferred) {
            console.log('Interação já foi respondida ou deferida');
            return;
        }

        try {
            await interaction.reply({
                content: '⚠️ Tem certeza que deseja limpar todo o estoque deste produto?',
                components: [
                    new ActionRowBuilder()
                        .addComponents(
                            new ButtonBuilder()
                                .setCustomId(`confirm_clear_stock_${productId}`)
                                .setLabel('Sim, Limpar')
                                .setStyle(ButtonStyle.Danger)
                                .setEmoji('✅'),
                            new ButtonBuilder()
                                .setCustomId(`cancel_clear_stock_${productId}`)
                                .setLabel('Cancelar')
                                .setStyle(ButtonStyle.Secondary)
                                .setEmoji('❌')
                        )
                ],
                ephemeral: true
            });
        } catch (error) {
            console.error('Erro ao mostrar confirmação de limpar estoque:', error.message);
        }
    }

    async handleConfigProductColor(interaction, productId) {
        if (interaction.replied || interaction.deferred) {
            console.log('Interação já foi respondida ou deferida');
            return;
        }

        try {
            const embed = new EmbedBuilder()
                .setTitle('🎨 Selecionar Cor da Embed')
                .setDescription('Escolha uma cor para a embed do produto:')
                .setColor(0x0099ff);

            const colorButtons = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`set_color_${productId}_blue`)
                        .setLabel('Azul')
                        .setStyle(ButtonStyle.Primary)
                        .setEmoji('🔵'),
                    new ButtonBuilder()
                        .setCustomId(`set_color_${productId}_green`)
                        .setLabel('Verde')
                        .setStyle(ButtonStyle.Success)
                        .setEmoji('🟢'),
                    new ButtonBuilder()
                        .setCustomId(`set_color_${productId}_red`)
                        .setLabel('Vermelho')
                        .setStyle(ButtonStyle.Danger)
                        .setEmoji('🔴'),
                    new ButtonBuilder()
                        .setCustomId(`set_color_${productId}_yellow`)
                        .setLabel('Amarelo')
                        .setStyle(ButtonStyle.Secondary)
                        .setEmoji('🟡'),
                    new ButtonBuilder()
                        .setCustomId(`set_color_${productId}_purple`)
                        .setLabel('Roxo')
                        .setStyle(ButtonStyle.Secondary)
                        .setEmoji('🟣')
                );

            await interaction.reply({
                embeds: [embed],
                components: [colorButtons],
                ephemeral: true
            });
        } catch (error) {
            console.error('Erro ao mostrar seletor de cor:', error.message);
        }
    }

    async handleConfigProductBuyButton(interaction, productId) {
        if (interaction.replied || interaction.deferred) {
            console.log('Interação já foi respondida ou deferida');
            return;
        }

        try {
            const userId = interaction.user.id;
            const userData = database.getUser(userId) || {};
            const product = userData.products.find(p => p.id === productId);

            if (!product) {
                await interaction.reply({ content: '❌ Produto não encontrado.', ephemeral: true });
                return;
            }

            const modal = new ModalBuilder()
                .setCustomId(`config_product_buy_button_modal_${productId}`)
                .setTitle('Configurar Botão de Compra');

            const buyButtonTextInput = new TextInputBuilder()
                .setCustomId('product_buy_button_text_input')
                .setLabel('Texto do Botão')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('Ex: Comprar Agora, Adquirir, Comprar')
                .setValue(product.details.buyButtonText || 'Comprar')
                .setRequired(true);

            const buyButtonEmojiInput = new TextInputBuilder()
                .setCustomId('product_buy_button_emoji_input')
                .setLabel('Emoji do Botão')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('Ex: 🛒, 💳, 🛍️, 💰')
                .setValue(product.details.buyButtonEmoji || '🛒')
                .setRequired(true);

            const buyButtonStyleInput = new TextInputBuilder()
                .setCustomId('product_buy_button_style_input')
                .setLabel('Cor do Botão')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('azul, cinza, verde, vermelho, link, premium')
                .setValue(product.details.buyButtonColor || 'verde')
                .setRequired(true);

            modal.addComponents(
                new ActionRowBuilder().addComponents(buyButtonTextInput),
                new ActionRowBuilder().addComponents(buyButtonEmojiInput),
                new ActionRowBuilder().addComponents(buyButtonStyleInput)
            );
            await interaction.showModal(modal);
        } catch (error) {
            console.error('Erro ao mostrar modal de botão de compra:', error.message);
        }
    }

    async handleConfigProductSave(interaction, productId) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const product = userData.products.find(p => p.id === productId);

        if (!product) {
            await interaction.reply({ content: '❌ Produto não encontrado.', ephemeral: true });
            return;
        }

        try {
            const guild = interaction.guild;
            if (!guild) {
                throw new Error('Não foi possível acessar o servidor.');
            }

            const channel = guild.channels.cache.get(product.channelId);
            if (!channel) {
                throw new Error('Canal do produto não encontrado.');
            }

            // Apagar a mensagem de configuração se existir
            if (product.embedMessageId) {
                try {
                    const configMessage = await channel.messages.fetch(product.embedMessageId);
                    if (configMessage) {
                        await configMessage.delete();
                    }
                } catch (error) {
                    console.log('Mensagem de configuração já foi deletada ou não encontrada');
                }
            }

            // Criar embed final do produto
            const productEmbed = new EmbedBuilder()
                .setTitle(product.name)
                .setDescription(product.details.description)
                .addFields(
                    { name: '💰 Preço', value: `R$ ${product.details.price.toFixed(2).replace('.', ',')}`, inline: true },
                    { name: '📦 Estoque', value: product.details.infiniteStock ? '♾️ Infinito' : `${product.details.stock} unidades`, inline: true }
                )
                .setColor(product.details.color || 0x0099ff)
                .setTimestamp();

            // Adicionar miniatura se configurada
            if (product.details.thumbnail) {
                productEmbed.setThumbnail(product.details.thumbnail);
            }

            // Adicionar banner se configurado
            if (product.details.banner) {
                productEmbed.setImage(product.details.banner);
            }

            // Mapear estilo do botão
            const getButtonStyle = (styleNumber) => {
                switch(styleNumber) {
                    case 1: return ButtonStyle.Primary;    // Azul
                    case 2: return ButtonStyle.Secondary;  // Cinza
                    case 3: return ButtonStyle.Success;    // Verde
                    case 4: return ButtonStyle.Danger;     // Vermelho
                    case 5: return ButtonStyle.Link;       // Link
                    case 6: return ButtonStyle.Premium;    // Premium
                    default: return ButtonStyle.Success;   // Verde (padrão)
                }
            };

            // Botões do produto final
            const productButtons = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`buy_product_${productId}`)
                        .setLabel(product.details.buyButtonText || 'Comprar')
                        .setStyle(getButtonStyle(product.details.buyButtonStyle || 3))
                        .setEmoji(product.details.buyButtonEmoji || '🛒'),
                    new ButtonBuilder()
                        .setCustomId(`edit_product_settings_${productId}`)
                        .setStyle(ButtonStyle.Secondary)
                        .setEmoji('⚙️')
                );

            // Enviar mensagem final do produto
            const finalMessage = await channel.send({
                embeds: [productEmbed],
                components: [productButtons]
            });

            // Atualizar o ID da mensagem no produto
            product.embedMessageId = finalMessage.id;
            database.saveUser(userId, userData);

            await interaction.reply({
                content: '✅ Produto salvo e publicado com sucesso!',
                ephemeral: true
            });

        } catch (error) {
            console.error(`Erro ao salvar produto: ${error.message}`);
            await interaction.reply({
                content: `❌ Erro ao salvar produto: ${error.message}`,
                ephemeral: true
            });
        }
    }

    async handleConfigProductDelete(interaction, productId) {
        if (interaction.replied || interaction.deferred) {
            console.log('Interação já foi respondida ou deferida');
            return;
        }

        try {
            await interaction.reply({
                content: '⚠️ Tem certeza que deseja apagar este produto? Esta ação não pode ser desfeita!',
                components: [
                    new ActionRowBuilder()
                        .addComponents(
                            new ButtonBuilder()
                                .setCustomId(`confirm_delete_product_${productId}`)
                                .setLabel('Sim, Apagar')
                                .setStyle(ButtonStyle.Danger)
                                .setEmoji('✅'),
                            new ButtonBuilder()
                                .setCustomId(`cancel_delete_product_${productId}`)
                                .setLabel('Cancelar')
                                .setStyle(ButtonStyle.Secondary)
                                .setEmoji('❌')
                        )
                ],
                ephemeral: true
            });
        } catch (error) {
            console.error('Erro ao mostrar confirmação de deletar produto:', error.message);
        }
    }

    async handleConfirmClearStock(interaction, productId) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const product = userData.products.find(p => p.id === productId);

        if (!product) {
            await interaction.update({ content: '❌ Produto não encontrado.', components: [] });
            return;
        }

        // Limpar estoque completamente
        product.details.stock = 0;
        product.details.stockItems = [];
        product.details.infiniteStock = false;
        product.details.stockTemplate = '';

        console.log(`🧹 Limpando estoque do produto ${product.name} (ID: ${productId})`);
        console.log(`📊 Dados após limpeza: ${JSON.stringify({
            stock: product.details.stock,
            stockItems: product.details.stockItems,
            infiniteStock: product.details.infiniteStock,
            stockTemplate: product.details.stockTemplate
        })}`);

        // Forçar salvamento múltiplo para garantir persistência
        database.saveUser(userId, userData);

        // Aguardar um pouco e salvar novamente para garantir
        setTimeout(() => {
            database.saveUser(userId, userData);
            console.log(`✅ Estoque limpo e dados salvos (dupla verificação) para usuário ${userId}`);
        }, 100);

        console.log(`✅ Estoque limpo e dados salvos para usuário ${userId}`);

        // Atualizar a embed de configuração no canal do produto
        try {
            const guild = interaction.guild;
            if (!guild) {
                throw new Error('Não foi possível acessar o servidor.');
            }

            const channel = guild.channels.cache.get(product.channelId);
            if (channel && product.embedMessageId) {
                const message = await channel.messages.fetch(product.embedMessageId);
                if (message) {
                    await this.updateProductConfigurationEmbed(message, product);
                }
            }

            await interaction.update({ content: '✅ Estoque limpo com sucesso!', components: [] });
        } catch (error) {
            console.error(`Erro ao limpar estoque: ${error.message}`);
            await interaction.update({ content: `❌ Erro ao limpar estoque: ${error.message}`, components: [] });
        }
    }

    async handleConfirmDeleteProduct(interaction, productId) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const product = userData.products.find(p => p.id === productId);

        if (!product) {
            await interaction.update({ content: '❌ Produto não encontrado.', components: [] });
            return;
        }

        try {
            const guild = interaction.guild;
            if (!guild) {
                throw new Error('Não foi possível acessar o servidor.');
            }

            // Deletar o canal do produto
            const channel = guild.channels.cache.get(product.channelId);
            if (channel) {
                await channel.delete();
            }

            // Remover produto da categoria
            const category = userData.categories.find(cat => cat.id === product.categoryId);
            if (category) {
                category.products = category.products.filter(pid => pid !== productId);
            }

            // Remover produto dos dados do usuário
            userData.products = userData.products.filter(p => p.id !== productId);
            database.saveUser(userId, userData);

            await interaction.update({ content: '✅ Produto apagado com sucesso!', components: [] });
        } catch (error) {
            console.error(`Erro ao apagar produto: ${error.message}`);
            await interaction.update({ content: `❌ Erro ao apagar produto: ${error.message}`, components: [] });
        }
    }

    async handleSetProductColor(interaction, productId, colorName) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const product = userData.products.find(p => p.id === productId);

        if (!product) {
            await interaction.update({ content: '❌ Produto não encontrado.', components: [] });
            return;
        }

        // Mapear cores
        const colorMap = {
            blue: 0x0099ff,
            green: 0x00ff00,
            red: 0xff0000,
            yellow: 0xffff00,
            purple: 0x9932cc
        };

        const color = colorMap[colorName] || 0x0099ff;
        product.details.color = color;
        database.saveUser(userId, userData);

        // Atualizar a embed de configuração no canal do produto
        try {
            const guild = interaction.guild;
            if (!guild) {
                throw new Error('Não foi possível acessar o servidor.');
            }

            const channel = guild.channels.cache.get(product.channelId);
            if (channel && product.embedMessageId) {
                const message = await channel.messages.fetch(product.embedMessageId);
                if (message) {
                    await this.updateProductConfigurationEmbed(message, product);
                }
            }

            await interaction.update({ content: `✅ Cor alterada para ${colorName}!`, components: [] });
        } catch (error) {
            console.error(`Erro ao alterar cor: ${error.message}`);
            await interaction.update({ content: `❌ Erro ao alterar cor: ${error.message}`, components: [] });
        }
    }

    async handleBuyProduct(interaction, productId) {
        const buyerId = interaction.user.id;
        const buyerUser = interaction.user;

        // Encontrar o produto em todos os usuários
        let product = null;
        let sellerId = null;

        // Verificar se o usuário pode criar um novo carrinho
        if (!(await this.canUserCreateCart(buyerId))) {
            // Encontrar o carrinho ativo do usuário
            let activeCart = null;
            for (const [, cartData] of global.carts.entries()) {
                if (cartData.buyerId === buyerId) {
                    activeCart = cartData;
                    break;
                }
            }

            if (activeCart) {
                const existingChannel = interaction.guild.channels.cache.get(activeCart.channelId);
                if (existingChannel) {
                    await interaction.reply({
                        content: `🛒 **Você já tem um carrinho ativo!**\n\nSeu carrinho está no canal ${existingChannel}.\nFinalize ou cancele sua compra atual antes de criar um novo carrinho.`,
                        ephemeral: true
                    });
                    return;
                }
            }
        }

        console.log(`🔍 Buscando produto com ID: ${productId}`);

        const allUsers = database.getAllUsers();
        console.log(`📊 Total de usuários no banco: ${allUsers.size}`);

        for (const [userId, userData] of allUsers.entries()) {
            console.log(`👤 Verificando usuário ${userId}, produtos: ${userData.products?.length || 0}`);
            if (userData.products && Array.isArray(userData.products)) {
                const foundProduct = userData.products.find(p => p.id === productId);
                if (foundProduct) {
                    product = foundProduct;
                    sellerId = userId;
                    console.log(`✅ Produto encontrado! Vendedor: ${sellerId}, Produto: ${product.name}`);
                    break;
                }
            }
        }

        if (!product) {
            console.log(`❌ Produto ${productId} não encontrado em nenhum usuário`);
            await interaction.reply({
                content: '❌ Produto não encontrado.',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        // Verificar se o sistema de vendas está ativado para este vendedor
        const sellerData = database.getUser(sellerId) || {};
        if (sellerData.salesSystemEnabled === false) {
            await interaction.reply({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('🚫 Sistema de Vendas Desativado')
                        .setDescription('O sistema de vendas está temporariamente desativado para este vendedor.')
                        .setColor(0xff0000)
                        .addFields(
                            { name: '📞 Contato:', value: `Entre em contato com <@${sellerId}> para mais informações.`, inline: false }
                        )
                        .setTimestamp()
                        .setFooter({ text: 'Sistema de Vendas' })
                ],
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        // Verificar se há estoque
        if (!product.details.infiniteStock && product.details.stock <= 0) {
            await interaction.reply({
                content: '❌ Este produto está fora de estoque no momento.',
                flags: 64 // MessageFlags.Ephemeral
            });
            return;
        }

        try {
            // Criar categoria "Carrinho" se não existir
            const guild = interaction.guild;
            let cartCategory = guild.channels.cache.find(c => c.type === 4 && c.name === 'Carrinho');

            if (!cartCategory) {
                cartCategory = await guild.channels.create({
                    name: 'Carrinho',
                    type: 4, // GUILD_CATEGORY
                    permissionOverwrites: [
                        {
                            id: guild.roles.everyone.id,
                            deny: ['ViewChannel']
                        }
                    ]
                });
            }

            // Criar categoria "Logs de Vendas" se não existir
            let logsCategory = guild.channels.cache.find(c => c.type === 4 && c.name === 'Logs de Vendas');

            if (!logsCategory) {
                logsCategory = await guild.channels.create({
                    name: 'Logs de Vendas',
                    type: 4, // GUILD_CATEGORY
                    permissionOverwrites: [
                        {
                            id: guild.roles.everyone.id,
                            allow: ['ViewChannel', 'ReadMessageHistory']
                        }
                    ]
                });
            }

            // Buscar ou criar canal geral de logs de vendas
            let logChannel = guild.channels.cache.find(c => c.type === 0 && c.name === 'logs-de-vendas' && c.parent?.id === logsCategory.id);

            if (!logChannel) {
                logChannel = await guild.channels.create({
                    name: 'logs-de-vendas',
                    type: 0, // GUILD_TEXT
                    parent: logsCategory.id,
                    permissionOverwrites: [
                        {
                            id: guild.roles.everyone.id,
                            allow: ['ViewChannel', 'ReadMessageHistory'],
                            deny: ['SendMessages']
                        }
                    ]
                });
            }

            // Gerar ID único da transação
            const transactionId = `TXN-${Date.now()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;

            // Enviar embed inicial no canal de log
            const logEmbed = new EmbedBuilder()
                .setTitle('📊 Nova Venda Iniciada')
                .setDescription(`**ID da Transação:** \`${transactionId}\`\n\nUm novo carrinho foi criado e está aguardando pagamento.`)
                .setColor(0xffa500)
                .addFields(
                    { name: '👤 Comprador:', value: `${buyerUser.username} (${buyerId})`, inline: true },
                    { name: '🏪 Vendedor:', value: `<@${sellerId}>`, inline: true },
                    { name: '📦 Produto:', value: product.name, inline: true },
                    { name: '💰 Preço unitário:', value: `R$ ${product.details.price.toFixed(2).replace('.', ',')}`, inline: true },
                    { name: '🛒 Quantidade:', value: '1', inline: true },
                    { name: '💰 Valor total:', value: `R$ ${product.details.price.toFixed(2).replace('.', ',')}`, inline: true },
                    { name: '📅 Data de criação:', value: new Date().toLocaleString('pt-BR'), inline: false },
                    { name: '🔄 Status:', value: '⏳ Aguardando pagamento', inline: false }
                )
                .setTimestamp()
                .setFooter({ text: `Sistema de Logs de Vendas • ID: ${transactionId}` });

            const logMessage = await logChannel.send({ embeds: [logEmbed] });

            // Criar canal privado para o carrinho
            const cartChannelName = `carrinho-de-${buyerUser.username}`.toLowerCase().replace(/[^a-z0-9-]/g, '');
            const cartChannel = await guild.channels.create({
                name: cartChannelName,
                type: 0, // GUILD_TEXT
                parent: cartCategory.id,
                permissionOverwrites: [
                    {
                        id: guild.roles.everyone.id,
                        deny: ['ViewChannel']
                    },
                    {
                        id: buyerId,
                        allow: ['ViewChannel', 'SendMessages', 'ReadMessageHistory']
                    },
                    {
                        id: sellerId,
                        allow: ['ViewChannel', 'SendMessages', 'ReadMessageHistory']
                    }
                ]
            });

            // Gerar código único do produto
            const productCode = this.generateProductCode();

            // Criar embed do carrinho
            const cartEmbed = new EmbedBuilder()
                .setTitle(`Carrinho de: ${buyerUser.username}`)
                .setDescription('Após fazer todas as edições nos produtos abaixo, clique em Ir para pagamento para poder pagar e recebe-los')
                .setColor(0x00bfff)
                .setTimestamp();

            // Botões principais do carrinho
            const cartButtons = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`cart_payment_${productId}_${buyerId}`)
                        .setLabel('Ir para pagamento')
                        .setStyle(ButtonStyle.Success)
                        .setEmoji('✅'),
                    new ButtonBuilder()
                        .setCustomId(`cart_cancel_${productId}_${buyerId}`)
                        .setLabel('Cancelar carrinho')
                        .setStyle(ButtonStyle.Danger)
                        .setEmoji('❌')
                );

            await cartChannel.send({
                embeds: [cartEmbed],
                components: [cartButtons]
            });

            // Criar embed do produto no carrinho
            const productEmbed = new EmbedBuilder()
                .setTitle(product.name)
                .setDescription(`**Código do produto:** ${productCode}`)
                .setColor(product.details.color || 0x0099ff)
                .addFields(
                    { name: '💰 Preço unitário:', value: `R$ ${product.details.price.toFixed(2).replace('.', ',')}`, inline: true },
                    { name: '🛒 Quantidade:', value: '1', inline: true },
                    { name: '💰 Valor total:', value: `R$ ${product.details.price.toFixed(2).replace('.', ',')}`, inline: true }
                )
                .setTimestamp();

            if (product.details.thumbnail) {
                productEmbed.setThumbnail(product.details.thumbnail);
            }

            // Botões do produto
            const productButtons = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`cart_edit_quantity_${productId}_${buyerId}`)
                        .setLabel('✏️')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId(`cart_remove_product_${productId}_${buyerId}`)
                        .setLabel('❌')
                        .setStyle(ButtonStyle.Danger)
                );

            await cartChannel.send({
                embeds: [productEmbed],
                components: [productButtons]
            });

            // Salvar informações do carrinho
            if (!global.carts) {
                global.carts = new Map();
            }

            global.carts.set(`${buyerId}_${productId}`, {
                buyerId,
                sellerId,
                productId,
                productCode,
                quantity: 1,
                channelId: cartChannel.id,
                logChannelId: logChannel.id,
                logMessageId: logMessage.id,
                transactionId: transactionId,
                createdAt: new Date().toISOString()
            });

            await interaction.reply({
                content: `🛒 **Carrinho criado!**\n\nSeu carrinho foi criado no canal ${cartChannel}.\nVocê pode editar a quantidade e finalizar a compra por lá.`,
                flags: 64 // MessageFlags.Ephemeral
            });

        } catch (error) {
            console.error('Erro ao criar carrinho:', error.message);
            await interaction.reply({
                content: '❌ Erro ao criar carrinho. Tente novamente.',
                flags: 64 // MessageFlags.Ephemeral
            });
        }
    }

    async handleEditProductSettings(interaction, productId) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const product = userData.products.find(p => p.id === productId);

        if (!product) {
            await interaction.reply({ content: '❌ Produto não encontrado.', ephemeral: true });
            return;
        }

        // Verificar se o usuário é o dono do produto
        if (interaction.user.id !== userId) {
            await interaction.reply({
                content: '❌ Apenas o dono do produto pode editá-lo.',
                ephemeral: true
            });
            return;
        }

        try {
            const guild = interaction.guild;
            if (!guild) {
                throw new Error('Não foi possível acessar o servidor.');
            }

            const channel = guild.channels.cache.get(product.channelId);
            if (!channel) {
                throw new Error('Canal do produto não encontrado.');
            }

            // Apagar a mensagem atual do produto
            if (product.embedMessageId) {
                try {
                    const currentMessage = await channel.messages.fetch(product.embedMessageId);
                    if (currentMessage) {
                        await currentMessage.delete();
                    }
                } catch (error) {
                    console.log('Mensagem atual já foi deletada ou não encontrada');
                }
            }

            // Enviar nova embed de configuração
            await this.sendProductConfigurationEmbed(channel, product, userId);

            await interaction.reply({
                content: '⚙️ Modo de configuração ativado! Use os botões no canal para editar o produto.',
                ephemeral: true
            });

        } catch (error) {
            console.error(`Erro ao ativar configurações: ${error.message}`);
            await interaction.reply({
                content: `❌ Erro ao ativar configurações: ${error.message}`,
                ephemeral: true
            });
        }
    }

    async handlePaymentMercadoPago(interaction, productId) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const product = userData.products.find(p => p.id === productId);

        if (!product) {
            await interaction.update({
                content: '❌ Produto não encontrado.',
                embeds: [],
                components: []
            });
            return;
        }

        // Verificar se o vendedor tem Mercado Pago configurado
        const botOwnerData = database.getUser(userData.userId || userId) || {};
        if (!botOwnerData.mercadoPago || !botOwnerData.mercadoPago.accessToken) {
            await interaction.update({
                content: '❌ O vendedor ainda não configurou o Mercado Pago. Tente outro método de pagamento.',
                embeds: [],
                components: []
            });
            return;
        }

        await interaction.update({
            content: '💳 **Pagamento via Mercado Pago**\n\n🔄 Gerando link de pagamento...\n\n*Esta funcionalidade será implementada em breve.*',
            embeds: [],
            components: []
        });
    }

    async handlePaymentPix(interaction, productId) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const product = userData.products.find(p => p.id === productId);

        if (!product) {
            await interaction.update({
                content: '❌ Produto não encontrado.',
                embeds: [],
                components: []
            });
            return;
        }

        await interaction.update({
            content: '📱 **Pagamento via PIX**\n\n🔄 Gerando código PIX...\n\n*Esta funcionalidade será implementada em breve.*',
            embeds: [],
            components: []
        });
    }

    async handleEditProductCategory(interaction, productId) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const product = userData.products?.find(p => p.id === productId);
        const categories = userData.categories || [];

        if (!product) {
            await interaction.reply({
                content: 'Produto não encontrado.',
                ephemeral: true
            });
            return;
        }

        if (categories.length === 0) {
            await interaction.reply({
                content: 'Você não possui categorias. Crie uma categoria primeiro.',
                ephemeral: true
            });
            return;
        }

        // Criar opções do select menu com as categorias
        const categoryOptions = categories.map(category => ({
            label: category.name,
            value: category.id,
            description: `${category.products?.length || 0} produtos`,
            default: category.id === product.categoryId
        }));

        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId(`select_new_category_${productId}`)
            .setPlaceholder('Selecione a nova categoria')
            .addOptions(categoryOptions);

        const row = new ActionRowBuilder().addComponents(selectMenu);

        const embed = new EmbedBuilder()
            .setTitle('📂 Alterar Categoria do Produto')
            .setDescription(`Selecione a nova categoria para o produto **${product.name}**:`)
            .setColor(0x2196f3)
            .addFields(
                {
                    name: '📦 Produto:',
                    value: product.name,
                    inline: true
                },
                {
                    name: '📂 Categoria atual:',
                    value: categories.find(cat => cat.id === product.categoryId)?.name || 'Não encontrada',
                    inline: true
                }
            )
            .setFooter({ text: 'Selecione uma categoria da lista abaixo' })
            .setTimestamp();

        await interaction.reply({ embeds: [embed], components: [row], ephemeral: true });
    }

    async handleUpdateProductCategory(interaction, productId, newCategoryId) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const product = userData.products?.find(p => p.id === productId);
        const oldCategory = userData.categories?.find(cat => cat.id === product?.categoryId);
        const newCategory = userData.categories?.find(cat => cat.id === newCategoryId);

        if (!product || !newCategory) {
            await interaction.update({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('❌ Erro')
                        .setDescription('Produto ou categoria não encontrados.')
                        .setColor(0xff0000)
                ],
                components: []
            });
            return;
        }

        // Se a categoria é a mesma, não fazer nada
        if (product.categoryId === newCategoryId) {
            await interaction.update({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('ℹ️ Categoria inalterada')
                        .setDescription(`O produto **${product.name}** já está na categoria **${newCategory.name}**.`)
                        .setColor(0x2196f3)
                ],
                components: []
            });
            return;
        }

        try {
            // Remover produto da categoria antiga
            if (oldCategory && oldCategory.products) {
                const productIndex = oldCategory.products.indexOf(productId);
                if (productIndex > -1) {
                    oldCategory.products.splice(productIndex, 1);
                }
            }

            // Adicionar produto à nova categoria
            if (!newCategory.products) {
                newCategory.products = [];
            }
            newCategory.products.push(productId);

            // Atualizar categoria do produto
            product.categoryId = newCategoryId;

            // Mover canal do Discord para a nova categoria (se existir)
            if (product.channelId) {
                try {
                    const guild = interaction.guild;
                    if (guild && newCategory.discordCategoryId) {
                        const channel = await guild.channels.fetch(product.channelId).catch(() => null);
                        if (channel) {
                            await channel.setParent(newCategory.discordCategoryId, {
                                reason: 'Produto movido para nova categoria'
                            });
                        }
                    }
                } catch (error) {
                    console.error(`Erro ao mover canal do produto: ${error.message}`);
                    // Continuar mesmo se falhar a movimentação do canal
                }
            }

            // Salvar dados atualizados
            database.saveUser(userId, userData);

            // Mostrar mensagem de sucesso
            await interaction.update({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('✅ Categoria alterada com sucesso!')
                        .setDescription(`O produto **${product.name}** foi movido para a categoria **${newCategory.name}**.`)
                        .setColor(0x00ff00)
                        .addFields(
                            {
                                name: '📊 Alterações realizadas:',
                                value: `• Produto movido de **${oldCategory?.name || 'Categoria anterior'}** para **${newCategory.name}**\n• Canal do Discord atualizado\n• Dados salvos com sucesso`,
                                inline: false
                            }
                        )
                ],
                components: []
            });

        } catch (error) {
            console.error(`Erro ao alterar categoria do produto: ${error.message}`);

            await interaction.update({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('❌ Erro ao alterar categoria')
                        .setDescription(`Ocorreu um erro ao alterar a categoria: ${error.message}`)
                        .setColor(0xff0000)
                ],
                components: []
            });
        }
    }

    async sendProductConfigurationEmbed(channel, product, userId) {
        // Criar embed de configuração do produto
        const configEmbed = new EmbedBuilder()
            .setTitle(`🛠️ Configuração do Produto: ${product.name}`)
            .setDescription('Configure todos os aspectos do seu produto usando os botões abaixo:')
            .setColor(product.details.color || 0x0099ff)
            .addFields(
                { name: '📝 Nome', value: product.name, inline: true },
                { name: '📄 Descrição', value: product.details.description, inline: true },
                { name: '💰 Preço', value: `R$ ${product.details.price.toFixed(2).replace('.', ',')}`, inline: true },
                { name: '🖼️ Miniatura', value: product.details.thumbnail ? 'Configurada' : 'Não configurada', inline: true },
                { name: '🎨 Banner', value: product.details.banner ? 'Configurado' : 'Não configurado', inline: true },
                { name: '📦 Estoque', value: product.details.infiniteStock ? '♾️ Infinito' : `${product.details.stock} unidades`, inline: true },
                { name: '🛒 Botão de Compra', value: `${product.details.buyButtonEmoji || '🛒'} ${product.details.buyButtonText || 'Comprar'}`, inline: true },
                { name: '👥 Cargo', value: product.details.roleId ? `<@&${product.details.roleId}>` : 'Não configurado', inline: true }
            )
            .setFooter({ text: 'Use os botões abaixo para configurar cada aspecto do produto' })
            .setTimestamp();

        // Se houver miniatura configurada, adicionar à embed
        if (product.details.thumbnail) {
            configEmbed.setThumbnail(product.details.thumbnail);
        }

        // Se houver banner configurado, adicionar à embed
        if (product.details.banner) {
            configEmbed.setImage(product.details.banner);
        }

        // Primeira linha - Informações básicas
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`config_product_name_${product.id}`)
                    .setLabel('Nome')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📝'),
                new ButtonBuilder()
                    .setCustomId(`config_product_description_${product.id}`)
                    .setLabel('Descrição')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📄'),
                new ButtonBuilder()
                    .setCustomId(`config_product_price_${product.id}`)
                    .setLabel('Preço')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('💰'),
                new ButtonBuilder()
                    .setCustomId(`config_product_color_${product.id}`)
                    .setLabel('Cor')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🎨')
            );

        // Segunda linha - Imagens
        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`config_product_thumbnail_${product.id}`)
                    .setLabel('Miniatura')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🖼️'),
                new ButtonBuilder()
                    .setCustomId(`config_product_banner_${product.id}`)
                    .setLabel('Banner')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🎨')
            );

        // Terceira linha - Estoque
        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`config_product_stock_add_${product.id}`)
                    .setLabel('Add Estoque')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('➕'),
                new ButtonBuilder()
                    .setCustomId(`config_product_stock_clear_${product.id}`)
                    .setLabel('Limpar Estoque')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🗑️'),
                new ButtonBuilder()
                    .setCustomId(`config_product_backup_${product.id}`)
                    .setLabel('Backup Estoque')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('☁️')
            );

        // Quarta linha - Configurações avançadas
        const row4 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`config_product_cargo_${product.id}`)
                    .setLabel('Cargo')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('👥'),
                new ButtonBuilder()
                    .setCustomId(`config_product_buy_button_${product.id}`)
                    .setLabel('Botão de Compra')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🛒')
            );

        // Quinta linha - Importar/Exportar e ações
        const row5 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`config_product_export_${product.id}`)
                    .setLabel('Exportar Configurações')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📤'),
                new ButtonBuilder()
                    .setCustomId(`config_product_import_${product.id}`)
                    .setLabel('Importar Configurações')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📥'),
                new ButtonBuilder()
                    .setCustomId(`config_product_save_${product.id}`)
                    .setLabel('Salvar')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('✅'),
                new ButtonBuilder()
                    .setCustomId(`config_product_delete_${product.id}`)
                    .setLabel('Apagar Produto')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('✖️')
            );

        // Enviar a mensagem de configuração
        const message = await channel.send({
            embeds: [configEmbed],
            components: [row1, row2, row3, row4, row5]
        });

        // Salvar o ID da mensagem no produto
        product.embedMessageId = message.id;
        const userData = database.getUser(userId) || {};
        database.saveUser(userId, userData);

        return message;
    }

    async handleUpdateProductConfigDetails(interaction, productId, field, newValue) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const product = userData.products.find(p => p.id === productId);

        if (!product) {
            await interaction.reply({ content: 'Produto não encontrado.', ephemeral: true });
            return;
        }

        // Atualizar o campo específico
        if (field === 'name') {
            product.name = newValue;
        } else {
            product.details[field] = newValue;
        }

        database.saveUser(userId, userData);

        // Atualizar a embed de configuração no canal do produto
        try {
            const guild = interaction.guild;
            if (!guild) {
                throw new Error('Não foi possível acessar o servidor.');
            }

            const channel = guild.channels.cache.get(product.channelId);
            if (channel && product.embedMessageId) {
                const message = await channel.messages.fetch(product.embedMessageId);
                if (message) {
                    // Recriar a embed de configuração atualizada
                    await this.updateProductConfigurationEmbed(message, product);
                }
            }

            await interaction.reply({ content: `✅ ${field === 'name' ? 'Nome' : field.charAt(0).toUpperCase() + field.slice(1)} atualizado com sucesso!`, ephemeral: true });
        } catch (error) {
            console.error(`Erro ao atualizar configuração do produto: ${error.message}`);
            await interaction.reply({ content: `Erro ao atualizar o produto: ${error.message}`, ephemeral: true });
        }
    }

    async handleUpdateProductBuyButtonConfig(interaction, productId, buttonText, buttonEmoji, buttonStyle, buttonColor) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const product = userData.products.find(p => p.id === productId);

        if (!product) {
            await interaction.reply({ content: '❌ Produto não encontrado.', ephemeral: true });
            return;
        }

        // Atualizar as configurações do botão de compra
        product.details.buyButtonText = buttonText;
        product.details.buyButtonEmoji = buttonEmoji;
        product.details.buyButtonStyle = buttonStyle;
        product.details.buyButtonColor = buttonColor;

        database.saveUser(userId, userData);

        // Atualizar a embed de configuração se existir
        const channel = interaction.guild.channels.cache.get(product.channelId);
        if (channel && product.embedMessageId) {
            try {
                const message = await channel.messages.fetch(product.embedMessageId);
                await this.updateProductConfigurationEmbed(message, product);
            } catch (error) {
                console.error('Erro ao atualizar embed de configuração:', error.message);
            }
        }

        // Mapear números para nomes de estilos em português
        const styleNames = {
            1: 'Azul',
            2: 'Cinza',
            3: 'Verde',
            4: 'Vermelho',
            5: 'Link',
            6: 'Premium'
        };

        await interaction.reply({
            content: `✅ Botão de compra atualizado com sucesso!\n• **Texto:** ${buttonText}\n• **Emoji:** ${buttonEmoji}\n• **Cor:** ${styleNames[buttonStyle]}`,
            ephemeral: true
        });
    }

    async handleAddProductStockItems(interaction, productId, stockItems, isInfinite) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const product = userData.products.find(p => p.id === productId);

        if (!product) {
            await interaction.reply({ content: '❌ Produto não encontrado.', ephemeral: true });
            return;
        }

        // Inicializar estrutura de estoque se não existir
        if (!product.details.stockItems) {
            product.details.stockItems = [];
        }

        // Configurar estoque infinito
        product.details.infiniteStock = isInfinite;

        if (!isInfinite) {
            // Adicionar novos itens ao estoque
            product.details.stockItems.push(...stockItems);
            product.details.stock = product.details.stockItems.length;
        } else {
            // Para estoque infinito, manter apenas um item como template
            if (stockItems.length > 0) {
                product.details.stockTemplate = stockItems[0];
            }
            product.details.stock = 999999; // Número alto para indicar infinito
        }

        database.saveUser(userId, userData);

        // Atualizar a embed de configuração no canal do produto
        try {
            const guild = interaction.guild;
            if (!guild) {
                throw new Error('Não foi possível acessar o servidor.');
            }

            const channel = guild.channels.cache.get(product.channelId);
            if (channel && product.embedMessageId) {
                const message = await channel.messages.fetch(product.embedMessageId);
                if (message) {
                    await this.updateProductConfigurationEmbed(message, product);
                }
            }

            const stockInfo = isInfinite
                ? '♾️ Estoque configurado como infinito!'
                : `✅ Adicionados ${stockItems.length} itens ao estoque! Total: ${product.details.stock}`;

            await interaction.reply({ content: stockInfo, ephemeral: true });
        } catch (error) {
            console.error(`Erro ao atualizar estoque: ${error.message}`);
            await interaction.reply({ content: `❌ Erro ao atualizar estoque: ${error.message}`, ephemeral: true });
        }
    }

    async handleConfigProductBackup(interaction, productId) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const product = userData.products.find(p => p.id === productId);

        if (!product) {
            await interaction.reply({ content: '❌ Produto não encontrado.', ephemeral: true });
            return;
        }

        try {
            // Forçar reload dos dados do banco para garantir dados atualizados
            const freshUserData = database.getUser(userId) || {};
            const freshProduct = freshUserData.products.find(p => p.id === productId);

            if (!freshProduct) {
                await interaction.reply({ content: '❌ Produto não encontrado após reload.', ephemeral: true });
                return;
            }

            const stockItems = freshProduct.details.stockItems || [];
            const isInfinite = freshProduct.details.infiniteStock || false;

            console.log(`📋 Backup solicitado para produto ${freshProduct.name} (ID: ${productId})`);
            console.log(`📊 Dados atuais: stockItems=${stockItems.length}, infiniteStock=${isInfinite}, stock=${freshProduct.details.stock}`);
            const stockTemplate = product.details.stockTemplate || '';

            if (stockItems.length === 0 && !isInfinite) {
                await interaction.reply({
                    content: '❌ **Nenhum item no estoque para fazer backup.**\n\nAdicione itens ao estoque primeiro usando o botão "Add Estoque".',
                    ephemeral: true
                });
                return;
            }

            if (stockItems.length === 0 && isInfinite && !stockTemplate) {
                await interaction.reply({
                    content: '❌ **Estoque infinito sem template configurado.**\n\nAdicione um template ao configurar o estoque infinito.',
                    ephemeral: true
                });
                return;
            }

            let backupContent = `📦 **BACKUP DO ESTOQUE - ${freshProduct.name.toUpperCase()}**\n`;
            backupContent += `📅 **Data:** ${new Date().toLocaleString('pt-BR')}\n`;
            backupContent += `♾️ **Estoque Infinito:** ${isInfinite ? 'Sim' : 'Não'}\n`;
            backupContent += `📊 **Total de Itens:** ${stockItems.length}\n\n`;

            if (isInfinite) {
                backupContent += `🔄 **Template (Estoque Infinito):**\n`;
                backupContent += `${freshProduct.details.stockTemplate || 'Nenhum template configurado'}\n\n`;
            }

            if (stockItems.length > 0) {
                backupContent += `📋 **ITENS DO ESTOQUE:**\n\`\`\`\n`;
                stockItems.forEach((item, index) => {
                    backupContent += `${index + 1}. ${item}\n`;
                });
                backupContent += `\`\`\`\n\n`;
            }

            backupContent += `💾 **Como usar este backup:**\n`;
            backupContent += `• Copie os itens acima\n`;
            backupContent += `• Cole no campo "Itens do Estoque" ao adicionar estoque\n`;
            backupContent += `• Configure "Estoque Infinito" como "${isInfinite ? 'sim' : 'não'}"`;

            // Dividir em mensagens se for muito longo
            if (backupContent.length > 2000) {
                const chunks = this.splitMessage(backupContent, 2000);
                for (let i = 0; i < chunks.length; i++) {
                    await interaction.reply({
                        content: chunks[i],
                        ephemeral: true
                    });
                    if (i === 0) {
                        // Usar followUp para mensagens subsequentes
                        interaction.reply = interaction.followUp;
                    }
                }
            } else {
                await interaction.reply({
                    content: backupContent,
                    ephemeral: true
                });
            }
        } catch (error) {
            console.error('Erro ao criar backup:', error.message);
            await interaction.reply({ content: '❌ Erro ao criar backup do estoque.', ephemeral: true });
        }
    }

    splitMessage(text, maxLength) {
        const chunks = [];
        let currentChunk = '';
        const lines = text.split('\n');

        for (const line of lines) {
            if ((currentChunk + line + '\n').length > maxLength) {
                if (currentChunk) {
                    chunks.push(currentChunk.trim());
                    currentChunk = '';
                }
                if (line.length > maxLength) {
                    // Se uma linha for muito longa, dividir ela também
                    const words = line.split(' ');
                    for (const word of words) {
                        if ((currentChunk + word + ' ').length > maxLength) {
                            if (currentChunk) {
                                chunks.push(currentChunk.trim());
                                currentChunk = '';
                            }
                        }
                        currentChunk += word + ' ';
                    }
                } else {
                    currentChunk = line + '\n';
                }
            } else {
                currentChunk += line + '\n';
            }
        }

        if (currentChunk.trim()) {
            chunks.push(currentChunk.trim());
        }

        return chunks;
    }

    async handleConfigProductCargo(interaction, productId) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const product = userData.products.find(p => p.id === productId);

        if (!product) {
            await interaction.reply({ content: '❌ Produto não encontrado.', ephemeral: true });
            return;
        }

        try {
            const modal = new ModalBuilder()
                .setCustomId(`config_product_cargo_modal_${productId}`)
                .setTitle('Configurar Cargo do Produto');

            const cargoInput = new TextInputBuilder()
                .setCustomId('product_cargo_input')
                .setLabel('ID do Cargo (opcional)')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('Ex: 1234567890123456789')
                .setValue(product.details.roleId || '')
                .setRequired(false);

            modal.addComponents(new ActionRowBuilder().addComponents(cargoInput));
            await interaction.showModal(modal);
        } catch (error) {
            console.error('Erro ao mostrar modal de cargo:', error.message);
        }
    }

    async handleConfigProductExport(interaction, productId) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const product = userData.products.find(p => p.id === productId);

        if (!product) {
            await interaction.reply({ content: '❌ Produto não encontrado.', ephemeral: true });
            return;
        }

        try {
            // Gerar código aleatório único
            const exportCode = this.generateExportCode();

            // Criar configuração exportável
            const exportData = {
                productName: product.name,
                details: {
                    description: product.details.description,
                    price: product.details.price,
                    thumbnail: product.details.thumbnail,
                    banner: product.details.banner,
                    color: product.details.color,
                    buyButtonText: product.details.buyButtonText,
                    buyButtonEmoji: product.details.buyButtonEmoji,
                    buyButtonStyle: product.details.buyButtonStyle,
                    buyButtonColor: product.details.buyButtonColor,
                    roleId: product.details.roleId
                },
                exportDate: new Date().toISOString(),
                version: '1.0'
            };

            // Salvar configuração exportada globalmente (não só para o usuário)
            if (!global.exportedConfigs) {
                global.exportedConfigs = new Map();
            }

            global.exportedConfigs.set(exportCode, {
                data: exportData,
                createdBy: userId,
                createdAt: new Date().toISOString(),
                expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 dias
            });

            await interaction.reply({
                content: `✅ **Configurações exportadas com sucesso!**\n\n🔑 **Código de Importação:**\n\`\`\`\n${exportCode}\n\`\`\`\n\n📦 **Produto:** ${product.name}\n📅 **Data:** ${new Date().toLocaleString('pt-BR')}\n⏰ **Válido por:** 30 dias\n\n💾 **Como usar:**\n• Copie o código acima\n• Use no botão "Importar Configurações" de outro produto\n• O código pode ser usado por qualquer pessoa`,
                ephemeral: true
            });
        } catch (error) {
            console.error('Erro ao exportar configurações:', error.message);
            await interaction.reply({ content: '❌ Erro ao exportar configurações.', ephemeral: true });
        }
    }

    generateExportCode() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';

        // Formato: XXXX-XXXX-XXXX (12 caracteres + 2 hífens)
        for (let i = 0; i < 12; i++) {
            if (i > 0 && i % 4 === 0) {
                result += '-';
            }
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }

        return result;
    }

    generateProductCode() {
        // Gerar código numérico de 16 dígitos para o produto
        let result = '';
        for (let i = 0; i < 16; i++) {
            result += Math.floor(Math.random() * 10);
        }
        return result;
    }

    async handleCartPayment(interaction, productId, buyerId) {
        try {
            const cartKey = `${buyerId}_${productId}`;
            const cartData = global.carts?.get(cartKey);

            if (!cartData) {
                await interaction.reply({ content: '❌ Carrinho não encontrado.', ephemeral: true });
                return;
            }

            // Buscar dados do produto
            const allUsers = database.getAllUsers();
            let product = null;
            for (const [, userData] of allUsers.entries()) {
                if (userData.products && Array.isArray(userData.products)) {
                    const foundProduct = userData.products.find(p => p.id === productId);
                    if (foundProduct) {
                        product = foundProduct;
                        break;
                    }
                }
            }

            if (!product) {
                await interaction.reply({ content: '❌ Produto não encontrado.', ephemeral: true });
                return;
            }

            // Obter canal do carrinho
            const channel = interaction.guild.channels.cache.get(cartData.channelId);
            if (!channel) {
                await interaction.reply({ content: '❌ Canal do carrinho não encontrado.', ephemeral: true });
                return;
            }

            // Apagar todas as mensagens do canal
            try {
                const messages = await channel.messages.fetch({ limit: 100 });
                await channel.bulkDelete(messages);
            } catch (error) {
                console.log('Erro ao apagar mensagens:', error.message);
                // Se não conseguir apagar em massa, apagar uma por uma
                try {
                    const messages = await channel.messages.fetch({ limit: 10 });
                    for (const message of messages.values()) {
                        await message.delete();
                    }
                } catch (deleteError) {
                    console.log('Erro ao apagar mensagens individualmente:', deleteError.message);
                }
            }

            const totalValue = product.details.price * cartData.quantity;

            // Criar embed de checkout
            const checkoutEmbed = new EmbedBuilder()
                .setTitle('💳 Checkout e Envio')
                .setDescription('Confira as informações sobre os produtos e escolha a forma que deseja receber seus produtos')
                .setColor(0x00ff00)
                .addFields(
                    { name: '💰 Valor total:', value: `R$ ${totalValue.toFixed(2).replace('.', ',')}`, inline: false },
                    { name: `📦 ${product.name}`, value: `Quantidade: ${cartData.quantity} | Preço unitário: R$ ${product.details.price.toFixed(2).replace('.', ',')}`, inline: false }
                )
                .setTimestamp();

            // Botões de checkout
            const checkoutButtons = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`checkout_mercadopago_${productId}_${buyerId}`)
                        .setLabel('Pagar com Mercado Pago')
                        .setStyle(ButtonStyle.Success)
                        .setEmoji('💳'),
                    new ButtonBuilder()
                        .setCustomId(`checkout_cancel_${productId}_${buyerId}`)
                        .setLabel('Voltar ao carrinho')
                        .setStyle(ButtonStyle.Danger)
                        .setEmoji('⬅️')
                );

            // Enviar nova mensagem de checkout no canal
            await channel.send({
                embeds: [checkoutEmbed],
                components: [checkoutButtons]
            });

            // Responder à interação
            await interaction.deferUpdate();

        } catch (error) {
            console.error('Erro no checkout:', error.message);
            await interaction.reply({ content: '❌ Erro ao processar checkout.', ephemeral: true });
        }
    }

    async handleCartCancel(interaction, productId, buyerId) {
        try {
            const cartKey = `${buyerId}_${productId}`;
            const cartData = global.carts?.get(cartKey);

            if (!cartData) {
                await interaction.reply({ content: '❌ Carrinho não encontrado.', ephemeral: true });
                return;
            }

            // Deletar canal do carrinho
            const channel = interaction.guild.channels.cache.get(cartData.channelId);
            if (channel) {
                await channel.delete('Carrinho cancelado pelo usuário');
            }

            // Deletar log de pagamento antes de remover carrinho
            if (cartData) {
                await this.deletePaymentLog(cartData, 'Carrinho cancelado pelo usuário');
            }

            // Remover carrinho da memória
            global.carts.delete(cartKey);

            // Limpar também qualquer pagamento pendente relacionado
            if (global.payments) {
                for (const [preferenceId, paymentData] of global.payments.entries()) {
                    if (paymentData.buyerId === buyerId && paymentData.productId === productId) {
                        global.payments.delete(preferenceId);
                        console.log(`🧹 Pagamento pendente ${preferenceId} removido devido ao cancelamento do carrinho`);
                    }
                }
            }

            await interaction.reply({
                content: '❌ **Carrinho cancelado!**\n\nO canal foi deletado e sua compra foi cancelada.',
                ephemeral: true
            });

        } catch (error) {
            console.error('Erro ao cancelar carrinho:', error.message);
            await interaction.reply({ content: '❌ Erro ao cancelar carrinho.', ephemeral: true });
        }
    }

    async handleCartEditQuantity(interaction, productId, buyerId) {
        try {
            const modal = new ModalBuilder()
                .setCustomId(`cart_quantity_modal_${productId}_${buyerId}`)
                .setTitle('Editar Quantidade');

            const quantityInput = new TextInputBuilder()
                .setCustomId('cart_quantity_input')
                .setLabel('Nova Quantidade')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('Ex: 2')
                .setRequired(true)
                .setMinLength(1)
                .setMaxLength(3);

            modal.addComponents(new ActionRowBuilder().addComponents(quantityInput));
            await interaction.showModal(modal);

        } catch (error) {
            console.error('Erro ao mostrar modal de quantidade:', error.message);
            await interaction.reply({ content: '❌ Erro ao abrir editor de quantidade.', ephemeral: true });
        }
    }

    async handleCartRemoveProduct(interaction, productId, buyerId) {
        try {
            const cartKey = `${buyerId}_${productId}`;
            const cartData = global.carts?.get(cartKey);

            if (!cartData) {
                await interaction.reply({ content: '❌ Carrinho não encontrado.', ephemeral: true });
                return;
            }

            // Deletar canal do carrinho
            const channel = interaction.guild.channels.cache.get(cartData.channelId);
            if (channel) {
                await channel.delete('Produto removido do carrinho');
            }

            // Remover carrinho da memória
            global.carts.delete(cartKey);

            // Limpar também qualquer pagamento pendente relacionado
            if (global.payments) {
                for (const [preferenceId, paymentData] of global.payments.entries()) {
                    if (paymentData.buyerId === buyerId && paymentData.productId === productId) {
                        global.payments.delete(preferenceId);
                        console.log(`🧹 Pagamento pendente ${preferenceId} removido devido à remoção do produto`);
                    }
                }
            }

            await interaction.reply({
                content: '🗑️ **Produto removido do carrinho!**\n\nO canal foi deletado.',
                ephemeral: true
            });

        } catch (error) {
            console.error('Erro ao remover produto do carrinho:', error.message);
            await interaction.reply({ content: '❌ Erro ao remover produto.', ephemeral: true });
        }
    }

    async handleCheckoutMercadoPago(interaction, productId, buyerId) {
        try {
            const cartKey = `${buyerId}_${productId}`;
            const cartData = global.carts?.get(cartKey);

            if (!cartData) {
                await interaction.reply({ content: '❌ Carrinho não encontrado.', ephemeral: true });
                return;
            }

            // Buscar dados do produto
            const allUsers = database.getAllUsers();
            let product = null;
            let sellerId = null;
            for (const [userId, userData] of allUsers.entries()) {
                if (userData.products && Array.isArray(userData.products)) {
                    const foundProduct = userData.products.find(p => p.id === productId);
                    if (foundProduct) {
                        product = foundProduct;
                        sellerId = userId;
                        break;
                    }
                }
            }

            if (!product) {
                await interaction.reply({ content: '❌ Produto não encontrado.', ephemeral: true });
                return;
            }

            // Buscar dados do vendedor para pegar credenciais do Mercado Pago
            const sellerCredentials = this.getUserCredentials(sellerId);

            console.log(`🔍 Verificando credenciais do vendedor ${sellerId}:`);
            console.log(`Access Token: ${sellerCredentials.accessToken ? 'Configurado' : 'Não configurado'}`);
            console.log(`Client Secret: ${sellerCredentials.clientSecret ? 'Configurado' : 'Não configurado'}`);

            if (!sellerCredentials.accessToken) {
                await interaction.reply({
                    content: '❌ O vendedor não configurou o Mercado Pago. Entre em contato com o suporte.',
                    ephemeral: true
                });
                return;
            }

            // Verificar estoque
            if (!product.details.infiniteStock && product.details.stock < cartData.quantity) {
                await interaction.reply({
                    content: `❌ Estoque insuficiente. Disponível: ${product.details.stock} unidades.`,
                    ephemeral: true
                });
                return;
            }

            const totalValue = product.details.price * cartData.quantity;

            // Criar preferência de pagamento no Mercado Pago
            const paymentData = {
                items: [{
                    title: product.name,
                    quantity: cartData.quantity,
                    unit_price: product.details.price,
                    currency_id: 'BRL'
                }],
                payer: {
                    email: `buyer_${buyerId}@discord.com`
                },
                payment_methods: {
                    excluded_payment_types: [
                        { id: "credit_card" },
                        { id: "debit_card" },
                        { id: "ticket" },
                        { id: "bank_transfer" },
                        { id: "atm" },
                        { id: "digital_currency" },
                        { id: "digital_wallet" }
                    ],
                    excluded_payment_methods: [
                        { id: "visa" },
                        { id: "master" },
                        { id: "amex" },
                        { id: "elo" },
                        { id: "hipercard" },
                        { id: "diners" },
                        { id: "bolbradesco" },
                        { id: "pec" }
                    ],
                    installments: 1
                },
                external_reference: `${buyerId}_${productId}_${Date.now()}`,
                back_urls: {
                    success: `https://discord.com/channels/${interaction.guild.id}/${cartData.channelId}`,
                    failure: `https://discord.com/channels/${interaction.guild.id}/${cartData.channelId}`,
                    pending: `https://discord.com/channels/${interaction.guild.id}/${cartData.channelId}`
                },
                auto_return: 'approved'
            };

            try {
                const response = await axios.post('https://api.mercadopago.com/checkout/preferences', paymentData, {
                    headers: {
                        'Authorization': `Bearer ${sellerCredentials.accessToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const preference = response.data;

                // Obter canal do carrinho e limpar mensagens
                const channel = interaction.guild.channels.cache.get(cartData.channelId);
                if (channel) {
                    // Apagar todas as mensagens do canal
                    try {
                        const messages = await channel.messages.fetch({ limit: 100 });
                        await channel.bulkDelete(messages);
                    } catch (error) {
                        console.log('Erro ao apagar mensagens:', error.message);
                    }

                    // Criar embed de pagamento
                    const paymentEmbed = new EmbedBuilder()
                        .setTitle('💳 Pagamento via Mercado Pago')
                        .setDescription('Escolha uma das opções abaixo para finalizar seu pagamento:')
                        .setColor(0x009ee3)
                        .addFields(
                            { name: '📦 Produto:', value: product.name, inline: true },
                            { name: '🛒 Quantidade:', value: cartData.quantity.toString(), inline: true },
                            { name: '💰 Valor total:', value: `R$ ${totalValue.toFixed(2).replace('.', ',')}`, inline: true },
                            { name: '📱 PIX:', value: 'Clique no botão abaixo para gerar o código PIX', inline: false },
                            { name: '🌐 Site do Mercado Pago:', value: 'Pague diretamente no site oficial', inline: false }
                        )
                        .setTimestamp();

                    // Botões de pagamento
                    const paymentButtons = new ActionRowBuilder()
                        .addComponents(
                            new ButtonBuilder()
                                .setCustomId(`generate_pix_${productId}_${buyerId}_${preference.id}`)
                                .setLabel('Gerar PIX')
                                .setStyle(ButtonStyle.Primary)
                                .setEmoji('📱'),
                            new ButtonBuilder()
                                .setURL(preference.init_point)
                                .setLabel('Pagar no Site')
                                .setStyle(ButtonStyle.Link)
                                .setEmoji('🌐'),
                            new ButtonBuilder()
                                .setCustomId(`checkout_cancel_${productId}_${buyerId}`)
                                .setLabel('Cancelar')
                                .setStyle(ButtonStyle.Danger)
                                .setEmoji('❌')
                        );

                    await channel.send({
                        embeds: [paymentEmbed],
                        components: [paymentButtons]
                    });

                    // Salvar dados do pagamento
                    if (!global.payments) {
                        global.payments = new Map();
                    }
                    global.payments.set(preference.id, {
                        buyerId,
                        sellerId,
                        productId,
                        cartData,
                        product,
                        preferenceId: preference.id,
                        status: 'pending',
                        createdAt: new Date().toISOString(),
                        accessToken: sellerCredentials.accessToken // Adicionar token para verificação
                    });

                    // Iniciar verificação periódica para pagamentos pelo site
                    this.startWebsitePaymentVerification(preference.id, sellerCredentials.accessToken);
                }

                await interaction.deferUpdate();

            } catch (mpError) {
                console.error('Erro ao criar pagamento no Mercado Pago:', mpError.response?.data || mpError.message);
                await interaction.reply({
                    content: '❌ Erro ao processar pagamento. Tente novamente ou entre em contato com o suporte.',
                    ephemeral: true
                });
            }

        } catch (error) {
            console.error('Erro no checkout Mercado Pago:', error.message);
            await interaction.reply({ content: '❌ Erro ao processar checkout.', ephemeral: true });
        }
    }

    async handleCheckoutDM(interaction, productId, buyerId) {
        try {
            const cartKey = `${buyerId}_${productId}`;
            const cartData = global.carts?.get(cartKey);

            if (!cartData) {
                await interaction.reply({ content: '❌ Carrinho não encontrado.', ephemeral: true });
                return;
            }

            // Buscar dados do produto
            const allUsers = database.getAllUsers();
            let product = null;
            let productOwnerId = null;
            for (const [userId, userData] of allUsers.entries()) {
                if (userData.products && Array.isArray(userData.products)) {
                    const foundProduct = userData.products.find(p => p.id === productId);
                    if (foundProduct) {
                        product = foundProduct;
                        productOwnerId = userId;
                        break;
                    }
                }
            }

            if (!product) {
                await interaction.reply({ content: '❌ Produto não encontrado.', ephemeral: true });
                return;
            }

            // Verificar estoque e processar compra
            if (!product.details.infiniteStock) {
                if (product.details.stock < cartData.quantity) {
                    await interaction.reply({ content: '❌ Estoque insuficiente.', ephemeral: true });
                    return;
                }

                // Reduzir estoque
                product.details.stock -= cartData.quantity;
                if (product.details.stockItems && product.details.stockItems.length > 0) {
                    const itemsToSend = product.details.stockItems.splice(0, cartData.quantity);

                    // Enviar itens por DM
                    try {
                        const buyer = await interaction.client.users.fetch(buyerId);
                        const totalValue = product.details.price * cartData.quantity;

                        let dmMessage = `🎉 **Compra realizada com sucesso!**\n\n`;
                        dmMessage += `📦 **Produto:** ${product.name}\n`;
                        dmMessage += `🛒 **Quantidade:** ${cartData.quantity}\n`;
                        dmMessage += `💰 **Valor total:** R$ ${totalValue.toFixed(2).replace('.', ',')}\n`;
                        dmMessage += `📅 **Data:** ${new Date().toLocaleString('pt-BR')}\n\n`;
                        dmMessage += `🎁 **Seus itens:**\n\`\`\`\n`;

                        itemsToSend.forEach((item, index) => {
                            dmMessage += `${index + 1}. ${item}\n`;
                        });

                        dmMessage += `\`\`\`\n\n✅ Obrigado pela compra!`;

                        await buyer.send(dmMessage);

                        // Salvar dados atualizados
                        if (productOwnerId) {
                            const ownerData = database.getUser(productOwnerId);
                            if (ownerData && ownerData.products) {
                                const productIndex = ownerData.products.findIndex(p => p.id === productId);
                                if (productIndex !== -1) {
                                    ownerData.products[productIndex] = product;
                                    database.saveUser(productOwnerId, ownerData);
                                }
                            }
                        }

                    } catch (dmError) {
                        console.error('Erro ao enviar DM:', dmError.message);
                        await interaction.reply({ content: '❌ Erro ao enviar mensagem privada. Verifique se suas DMs estão abertas.', ephemeral: true });
                        return;
                    }
                }
            }

            // Obter canal do carrinho e limpar mensagens
            const channel = interaction.guild.channels.cache.get(cartData.channelId);
            if (channel) {
                // Apagar todas as mensagens do canal
                try {
                    const messages = await channel.messages.fetch({ limit: 100 });
                    await channel.bulkDelete(messages);
                } catch (error) {
                    console.log('Erro ao apagar mensagens:', error.message);
                    // Se não conseguir apagar em massa, apagar uma por uma
                    try {
                        const messages = await channel.messages.fetch({ limit: 10 });
                        for (const message of messages.values()) {
                            await message.delete();
                        }
                    } catch (deleteError) {
                        console.log('Erro ao apagar mensagens individualmente:', deleteError.message);
                    }
                }

                // Criar embed de confirmação
                const confirmationEmbed = new EmbedBuilder()
                    .setTitle('✅ Compra Finalizada!')
                    .setDescription('Sua compra foi processada com sucesso!')
                    .setColor(0x00ff00)
                    .addFields(
                        { name: '📦 Produto:', value: product.name, inline: true },
                        { name: '🛒 Quantidade:', value: cartData.quantity.toString(), inline: true },
                        { name: '💰 Valor total:', value: `R$ ${(product.details.price * cartData.quantity).toFixed(2).replace('.', ',')}`, inline: true },
                        { name: '📅 Data da compra:', value: new Date().toLocaleString('pt-BR'), inline: false },
                        { name: '📬 Entrega:', value: 'Os itens foram enviados para sua DM', inline: false }
                    )
                    .setTimestamp();

                // Botão para fechar o canal
                const closeButton = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`close_purchase_channel_${productId}_${buyerId}`)
                            .setLabel('Fechar Canal')
                            .setStyle(ButtonStyle.Secondary)
                            .setEmoji('🗑️')
                    );

                await channel.send({
                    embeds: [confirmationEmbed],
                    components: [closeButton]
                });

                // Aguardar 30 segundos e deletar o canal automaticamente
                setTimeout(async () => {
                    try {
                        await channel.delete('Compra finalizada - limpeza automática');
                    } catch (error) {
                        console.log('Canal já foi deletado ou erro ao deletar:', error.message);
                    }
                }, 30000);
            }

            // Remover carrinho da memória
            global.carts.delete(cartKey);

            await interaction.reply({
                content: '✅ **Compra processada!**\n\nVerifique o canal para mais detalhes e sua DM para os itens.',
                ephemeral: true
            });

        } catch (error) {
            console.error('Erro no checkout DM:', error.message);
            await interaction.reply({ content: '❌ Erro ao processar compra.', ephemeral: true });
        }
    }

    async handleCheckoutWhatsapp(interaction, productId, buyerId) {
        await interaction.reply({
            content: '📱 **Checkout via WhatsApp**\n\n🔄 Esta funcionalidade será implementada em breve.\n\nPor enquanto, use a opção "Mensagem via DM".',
            ephemeral: true
        });
    }

    async handleCheckoutEmail(interaction, productId, buyerId) {
        await interaction.reply({
            content: '📧 **Checkout via E-mail**\n\n🔄 Esta funcionalidade será implementada em breve.\n\nPor enquanto, use a opção "Mensagem via DM".',
            ephemeral: true
        });
    }

    async handleCheckoutCancel(interaction, productId, buyerId) {
        try {
            const cartKey = `${buyerId}_${productId}`;
            const cartData = global.carts?.get(cartKey);

            if (!cartData) {
                await interaction.reply({ content: '❌ Carrinho não encontrado.', ephemeral: true });
                return;
            }

            // Buscar dados do produto
            const allUsers = database.getAllUsers();
            let product = null;
            for (const [, userData] of allUsers.entries()) {
                if (userData.products && Array.isArray(userData.products)) {
                    const foundProduct = userData.products.find(p => p.id === productId);
                    if (foundProduct) {
                        product = foundProduct;
                        break;
                    }
                }
            }

            if (!product) {
                await interaction.reply({ content: '❌ Produto não encontrado.', ephemeral: true });
                return;
            }

            // Obter canal do carrinho
            const channel = interaction.guild.channels.cache.get(cartData.channelId);
            if (!channel) {
                await interaction.reply({ content: '❌ Canal do carrinho não encontrado.', ephemeral: true });
                return;
            }

            // Apagar todas as mensagens do canal
            try {
                const messages = await channel.messages.fetch({ limit: 100 });
                await channel.bulkDelete(messages);
            } catch (error) {
                console.log('Erro ao apagar mensagens:', error.message);
                // Se não conseguir apagar em massa, apagar uma por uma
                try {
                    const messages = await channel.messages.fetch({ limit: 10 });
                    for (const message of messages.values()) {
                        await message.delete();
                    }
                } catch (deleteError) {
                    console.log('Erro ao apagar mensagens individualmente:', deleteError.message);
                }
            }

            // Recriar interface do carrinho
            const buyerUser = await interaction.client.users.fetch(buyerId);

            // Criar embed do carrinho
            const cartEmbed = new EmbedBuilder()
                .setTitle(`Carrinho de: ${buyerUser.username}`)
                .setDescription('Após fazer todas as edições nos produtos abaixo, clique em Ir para pagamento para poder pagar e recebe-los')
                .setColor(0x00bfff)
                .setTimestamp();

            // Botões principais do carrinho
            const cartButtons = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`cart_payment_${productId}_${buyerId}`)
                        .setLabel('Ir para pagamento')
                        .setStyle(ButtonStyle.Success)
                        .setEmoji('✅'),
                    new ButtonBuilder()
                        .setCustomId(`cart_cancel_${productId}_${buyerId}`)
                        .setLabel('Cancelar carrinho')
                        .setStyle(ButtonStyle.Danger)
                        .setEmoji('❌')
                );

            await channel.send({
                embeds: [cartEmbed],
                components: [cartButtons]
            });

            // Criar embed do produto no carrinho
            const totalValue = product.details.price * cartData.quantity;
            const productEmbed = new EmbedBuilder()
                .setTitle(product.name)
                .setDescription(`**Código do produto:** ${cartData.productCode}`)
                .setColor(product.details.color || 0x0099ff)
                .addFields(
                    { name: '💰 Preço unitário:', value: `R$ ${product.details.price.toFixed(2).replace('.', ',')}`, inline: true },
                    { name: '🛒 Quantidade:', value: cartData.quantity.toString(), inline: true },
                    { name: '💰 Valor total:', value: `R$ ${totalValue.toFixed(2).replace('.', ',')}`, inline: true }
                )
                .setTimestamp();

            if (product.details.thumbnail) {
                productEmbed.setThumbnail(product.details.thumbnail);
            }

            // Botões do produto
            const productButtons = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`cart_edit_quantity_${productId}_${buyerId}`)
                        .setLabel('✏️')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId(`cart_remove_product_${productId}_${buyerId}`)
                        .setLabel('❌')
                        .setStyle(ButtonStyle.Danger)
                );

            await channel.send({
                embeds: [productEmbed],
                components: [productButtons]
            });

            await interaction.reply({
                content: '⬅️ **Voltando ao carrinho...**',
                ephemeral: true
            });

        } catch (error) {
            console.error('Erro ao voltar ao carrinho:', error.message);
            await interaction.reply({ content: '❌ Erro ao voltar ao carrinho.', ephemeral: true });
        }
    }

    async handleUpdateCartQuantity(interaction, productId, buyerId, newQuantity) {
        try {
            const cartKey = `${buyerId}_${productId}`;
            const cartData = global.carts?.get(cartKey);

            if (!cartData) {
                await interaction.reply({ content: '❌ Carrinho não encontrado.', ephemeral: true });
                return;
            }

            // Buscar dados do produto
            const allUsers = database.getAllUsers();
            let product = null;
            for (const [, userData] of allUsers.entries()) {
                if (userData.products && Array.isArray(userData.products)) {
                    const foundProduct = userData.products.find(p => p.id === productId);
                    if (foundProduct) {
                        product = foundProduct;
                        break;
                    }
                }
            }

            if (!product) {
                await interaction.reply({ content: '❌ Produto não encontrado.', ephemeral: true });
                return;
            }

            // Verificar se há estoque suficiente (se não for infinito)
            if (!product.details.infiniteStock && newQuantity > product.details.stock) {
                await interaction.reply({
                    content: `❌ Estoque insuficiente. Disponível: ${product.details.stock} unidades.`,
                    ephemeral: true
                });
                return;
            }

            // Atualizar quantidade no carrinho
            cartData.quantity = newQuantity;
            global.carts.set(cartKey, cartData);

            // Atualizar embed do produto no canal do carrinho
            const channel = interaction.guild.channels.cache.get(cartData.channelId);
            if (channel) {
                const messages = await channel.messages.fetch({ limit: 10 });
                const productMessage = messages.find(msg =>
                    msg.embeds.length > 0 &&
                    msg.embeds[0].title === product.name
                );

                if (productMessage) {
                    const totalValue = product.details.price * newQuantity;

                    const updatedEmbed = new EmbedBuilder()
                        .setTitle(product.name)
                        .setDescription(`**Código do produto:** ${cartData.productCode}`)
                        .setColor(product.details.color || 0x0099ff)
                        .addFields(
                            { name: '💰 Preço unitário:', value: `R$ ${product.details.price.toFixed(2).replace('.', ',')}`, inline: true },
                            { name: '🛒 Quantidade:', value: newQuantity.toString(), inline: true },
                            { name: '💰 Valor total:', value: `R$ ${totalValue.toFixed(2).replace('.', ',')}`, inline: true }
                        )
                        .setTimestamp();

                    if (product.details.thumbnail) {
                        updatedEmbed.setThumbnail(product.details.thumbnail);
                    }

                    await productMessage.edit({ embeds: [updatedEmbed] });
                }
            }

            await interaction.reply({
                content: `✅ **Quantidade atualizada!**\n\n🛒 **Nova quantidade:** ${newQuantity}\n💰 **Novo valor total:** R$ ${(product.details.price * newQuantity).toFixed(2).replace('.', ',')}`,
                ephemeral: true
            });

        } catch (error) {
            console.error('Erro ao atualizar quantidade:', error.message);
            await interaction.reply({ content: '❌ Erro ao atualizar quantidade.', ephemeral: true });
        }
    }

    async handleClosePurchaseChannel(interaction, productId, buyerId) {
        try {
            await interaction.reply({
                content: '🗑️ **Fechando canal...**\n\nO canal será deletado em 3 segundos.',
                ephemeral: true
            });

            // Aguardar 3 segundos e deletar o canal
            setTimeout(async () => {
                try {
                    await interaction.channel.delete('Canal fechado pelo usuário');
                } catch (error) {
                    console.log('Erro ao deletar canal:', error.message);
                }
            }, 3000);

        } catch (error) {
            console.error('Erro ao fechar canal:', error.message);
            await interaction.reply({ content: '❌ Erro ao fechar canal.', ephemeral: true });
        }
    }

    async handleGeneratePix(interaction, productId, buyerId, preferenceId) {
        try {
            const paymentData = global.payments?.get(preferenceId);
            if (!paymentData) {
                await interaction.reply({ content: '❌ Dados do pagamento não encontrados.', ephemeral: true });
                return;
            }

            const { sellerId, product, cartData } = paymentData;
            const sellerCredentials = this.getUserCredentials(sellerId);
            const totalValue = product.details.price * cartData.quantity;

            // Verificar se o vendedor tem bancos bloqueados configurados
            const sellerData = database.getUser(sellerId) || {};
            const bancosBloqueados = sellerData.bancosBloqueados || [];

            if (bancosBloqueados.length > 0) {
                // Lista de bancos disponíveis para mostrar quais estão bloqueados
                const bancosDisponiveis = {
                    'inter': '🟠 Banco Inter',
                    'nubank': '🟣 Nubank',
                    'picpay': '🟢 PicPay',
                    'mercadopago': '💙 Mercado Pago',
                    'c6bank': '⚫ C6 Bank',
                    'neon': '🔵 Neon',
                    'next': '🟤 Next',
                    'original': '🟡 Banco Original',
                    'digio': '🔴 Digio',
                    'will': '🟢 Will Bank'
                };

                const bancosBloqueadosNomes = bancosBloqueados
                    .map(bancoId => bancosDisponiveis[bancoId] || bancoId)
                    .join('\n');

                const warningEmbed = new EmbedBuilder()
                    .setTitle('⚠️ Aviso sobre Bancos Bloqueados')
                    .setDescription('**O vendedor configurou restrições para alguns bancos.**\n\nPor favor, **NÃO** use os bancos listados abaixo para fazer o pagamento PIX:')
                    .setColor(0xff9500)
                    .addFields(
                        {
                            name: '🚫 Bancos Bloqueados:',
                            value: bancosBloqueadosNomes,
                            inline: false
                        },
                        {
                            name: '✅ Recomendação:',
                            value: 'Use bancos tradicionais como Itaú, Bradesco, Santander, Caixa, Banco do Brasil ou outros não listados acima.',
                            inline: false
                        },
                        {
                            name: '🛡️ Motivo:',
                            value: 'Esta medida visa proteger contra golpes e chargebacks indevidos.',
                            inline: false
                        }
                    )
                    .setFooter({ text: 'Clique em "Continuar" para gerar o PIX mesmo assim' })
                    .setTimestamp();

                const warningButtons = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`continue_pix_${productId}_${buyerId}_${preferenceId}`)
                            .setLabel('Continuar Mesmo Assim')
                            .setStyle(ButtonStyle.Primary)
                            .setEmoji('⚠️'),
                        new ButtonBuilder()
                            .setCustomId(`cancel_pix_${productId}_${buyerId}`)
                            .setLabel('Cancelar')
                            .setStyle(ButtonStyle.Secondary)
                            .setEmoji('❌')
                    );

                await interaction.update({
                    embeds: [warningEmbed],
                    components: [warningButtons]
                });
                return;
            }

            // Criar pagamento PIX via Mercado Pago
            const pixPaymentData = {
                transaction_amount: totalValue,
                description: `${product.name} - Quantidade: ${cartData.quantity}`,
                payment_method_id: 'pix',
                payer: {
                    email: `buyer_${buyerId}@discord.com`
                },
                external_reference: `${buyerId}_${productId}_${Date.now()}`
            };

            try {
                // Gerar chave de idempotência única
                const idempotencyKey = `${buyerId}_${productId}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

                const response = await axios.post('https://api.mercadopago.com/v1/payments', pixPaymentData, {
                    headers: {
                        'Authorization': `Bearer ${sellerCredentials.accessToken}`,
                        'Content-Type': 'application/json',
                        'X-Idempotency-Key': idempotencyKey
                    }
                });

                const payment = response.data;
                const pixCode = payment.point_of_interaction?.transaction_data?.qr_code;
                const pixCodeBase64 = payment.point_of_interaction?.transaction_data?.qr_code_base64;

                if (!pixCode) {
                    await interaction.reply({ content: '❌ Erro ao gerar código PIX.', ephemeral: true });
                    return;
                }

                // Atualizar dados do pagamento
                paymentData.paymentId = payment.id;
                paymentData.pixCode = pixCode;
                global.payments.set(preferenceId, paymentData);

                // Criar embed do PIX
                const pixEmbed = new EmbedBuilder()
                    .setTitle('📱 Pagamento PIX')
                    .setDescription('Use o código PIX abaixo para finalizar seu pagamento:')
                    .setColor(0x00ff00)
                    .addFields(
                        { name: '💰 Valor:', value: `R$ ${totalValue.toFixed(2).replace('.', ',')}`, inline: true },
                        { name: '⏰ Validade:', value: '30 minutos', inline: true },
                        { name: '📋 Código PIX:', value: `\`\`\`${pixCode}\`\`\``, inline: false },
                        { name: '📱 Como pagar:', value: '1. Abra seu app do banco\n2. Escolha PIX\n3. Copie e cole o código acima\n4. Confirme o pagamento', inline: false }
                    )
                    .setTimestamp();

                if (pixCodeBase64) {
                    // Criar arquivo temporário com QR Code
                    const qrBuffer = Buffer.from(pixCodeBase64, 'base64');
                    pixEmbed.setImage('attachment://qrcode.png');
                }

                // Botões
                const pixButtons = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`copy_pix_${preferenceId}`)
                            .setLabel('Copiar Código PIX')
                            .setStyle(ButtonStyle.Primary)
                            .setEmoji('📋'),
                        new ButtonBuilder()
                            .setCustomId(`check_payment_${preferenceId}`)
                            .setLabel('Verificar Pagamento')
                            .setStyle(ButtonStyle.Success)
                            .setEmoji('🔍')
                    );

                const messageData = {
                    embeds: [pixEmbed],
                    components: [pixButtons]
                };

                if (pixCodeBase64) {
                    const qrBuffer = Buffer.from(pixCodeBase64, 'base64');
                    messageData.files = [{
                        attachment: qrBuffer,
                        name: 'qrcode.png'
                    }];
                }

                await interaction.update(messageData);

                // Iniciar verificação automática de pagamento
                this.startPaymentVerification(preferenceId, payment.id, sellerCredentials.accessToken);

            } catch (mpError) {
                console.error('Erro ao criar pagamento PIX:', mpError.response?.data || mpError.message);
                await interaction.reply({
                    content: '❌ Erro ao gerar PIX. Tente novamente.',
                    ephemeral: true
                });
            }

        } catch (error) {
            console.error('Erro ao gerar PIX:', error.message);
            await interaction.reply({ content: '❌ Erro ao gerar PIX.', ephemeral: true });
        }
    }

    startPaymentVerification(preferenceId, paymentId, accessToken) {
        const checkPayment = async () => {
            try {
                const response = await axios.get(`https://api.mercadopago.com/v1/payments/${paymentId}`, {
                    headers: {
                        'Authorization': `Bearer ${accessToken}`
                    }
                });

                const payment = response.data;
                const paymentData = global.payments?.get(preferenceId);

                if (!paymentData) {
                    console.log('Dados do pagamento não encontrados, parando verificação');
                    return;
                }

                if (payment.status === 'approved') {
                    console.log(`✅ Pagamento aprovado! ID: ${paymentId}`);

                    // Verificar se o pagamento veio de um banco bloqueado
                    const bankBlocked = await this.checkBlockedBank(payment, paymentData.sellerId);

                    if (bankBlocked) {
                        console.log(`🚫 Pagamento de banco bloqueado detectado! Banco: ${bankBlocked.bankName}`);
                        await this.handleBlockedBankPayment(payment, paymentData, bankBlocked);
                        global.payments.delete(preferenceId);
                        return;
                    }

                    await this.processApprovedPayment(paymentData);
                    global.payments.delete(preferenceId);
                    return;
                }

                if (payment.status === 'cancelled' || payment.status === 'rejected') {
                    console.log(`❌ Pagamento ${payment.status}: ${paymentId}`);
                    global.payments.delete(preferenceId);
                    return;
                }

                // Se ainda está pendente, verificar novamente em 10 segundos
                if (payment.status === 'pending') {
                    setTimeout(checkPayment, 10000);
                }

            } catch (error) {
                console.error('Erro ao verificar pagamento:', error.message);
                // Tentar novamente em 15 segundos em caso de erro
                setTimeout(checkPayment, 15000);
            }
        };

        // Iniciar verificação
        setTimeout(checkPayment, 5000);
    }

    startWebsitePaymentVerification(preferenceId, accessToken) {
        console.log(`🌐 Iniciando verificação de pagamento pelo site: ${preferenceId}`);

        let verificationCount = 0;
        const maxVerifications = 120; // 120 verificações = 30 minutos (15s cada)

        const checkWebsitePayment = async () => {
            try {
                verificationCount++;

                // Parar verificação após timeout
                if (verificationCount > maxVerifications) {
                    console.log(`⏰ Timeout na verificação de pagamento pelo site: ${preferenceId}`);
                    global.payments.delete(preferenceId);
                    return;
                }

                const paymentData = global.payments?.get(preferenceId);

                if (!paymentData) {
                    console.log('Dados do pagamento não encontrados, parando verificação do site');
                    return;
                }

                // Buscar pagamentos relacionados à preferência
                const externalRef = `${paymentData.buyerId}_${paymentData.productId}_`;
                const response = await axios.get(`https://api.mercadopago.com/v1/payments/search`, {
                    headers: {
                        'Authorization': `Bearer ${accessToken}`
                    },
                    params: {
                        external_reference: externalRef,
                        sort: 'date_created',
                        criteria: 'desc',
                        range: 'date_created',
                        begin_date: new Date(paymentData.createdAt).toISOString(),
                        end_date: new Date().toISOString()
                    }
                });

                const payments = response.data.results;
                console.log(`🔍 Encontrados ${payments.length} pagamentos para a preferência ${preferenceId}`);

                // Verificar se há algum pagamento aprovado
                const approvedPayment = payments.find(payment => payment.status === 'approved');

                if (approvedPayment) {
                    console.log(`✅ Pagamento aprovado encontrado pelo site! ID: ${approvedPayment.id}`);

                    // Verificar se é banco bloqueado
                    const bankBlocked = await this.checkBlockedBank(approvedPayment, paymentData.sellerId);

                    if (bankBlocked) {
                        console.log(`🚫 Pagamento de banco bloqueado detectado no site! Banco: ${bankBlocked.bankName}`);
                        await this.handleBlockedBankPayment(approvedPayment, paymentData, bankBlocked);
                        global.payments.delete(preferenceId);
                        return;
                    }

                    // Processar pagamento aprovado
                    await this.processApprovedPayment(paymentData);
                    global.payments.delete(preferenceId);
                    return;
                }

                // Verificar se há pagamento rejeitado ou cancelado
                const failedPayment = payments.find(payment =>
                    payment.status === 'cancelled' ||
                    payment.status === 'rejected'
                );

                if (failedPayment) {
                    console.log(`❌ Pagamento ${failedPayment.status} encontrado: ${failedPayment.id}`);
                    global.payments.delete(preferenceId);
                    return;
                }

                // Se ainda está pendente, verificar novamente em 15 segundos
                const pendingPayment = payments.find(payment => payment.status === 'pending');
                if (pendingPayment || payments.length === 0) {
                    console.log(`⏳ Pagamento ainda pendente ou não encontrado, verificando novamente em 15s`);
                    setTimeout(checkWebsitePayment, 15000);
                }

            } catch (error) {
                console.error('Erro ao verificar pagamento do site:', error.message);
                // Tentar novamente em 20 segundos em caso de erro
                setTimeout(checkWebsitePayment, 20000);
            }
        };

        // Iniciar verificação após 10 segundos
        setTimeout(checkWebsitePayment, 10000);
    }

    async processApprovedPayment(paymentData) {
        try {
            const { buyerId, sellerId, productId, cartData, product } = paymentData;

            // Calcular taxa de comissão (8% sobre o valor total)
            const totalValue = product.details.price * cartData.quantity;
            const commissionRate = 0.08; // 8%
            const commissionAmount = totalValue * commissionRate;
            const sellerAmount = totalValue - commissionAmount;

            console.log(`💰 Processando comissão: Valor total: R$ ${totalValue.toFixed(2)}, Comissão (8%): R$ ${commissionAmount.toFixed(2)}, Vendedor recebe: R$ ${sellerAmount.toFixed(2)}`);

            // Registrar comissão no sistema
            await this.registerCommission(sellerId, productId, totalValue, commissionAmount, paymentData);

            // Reduzir estoque se não for infinito
            if (!product.details.infiniteStock) {
                product.details.stock -= cartData.quantity;

                // Enviar itens se houver no estoque
                if (product.details.stockItems && product.details.stockItems.length > 0) {
                    const itemsToSend = product.details.stockItems.splice(0, cartData.quantity);

                    // Enviar por DM em formato embed
                    try {
                        const buyer = await this.client.users.fetch(buyerId);
                        const totalValue = product.details.price * cartData.quantity;

                        const deliveryEmbed = new EmbedBuilder()
                            .setTitle('🎉 Compra Aprovada!')
                            .setDescription('Seu pagamento foi confirmado e seus itens estão prontos!')
                            .setColor(0x00ff00)
                            .addFields(
                                { name: '📦 Produto:', value: product.name, inline: true },
                                { name: '🛒 Quantidade:', value: cartData.quantity.toString(), inline: true },
                                { name: '💰 Valor pago:', value: `R$ ${totalValue.toFixed(2).replace('.', ',')}`, inline: true },
                                { name: '📅 Data:', value: new Date().toLocaleString('pt-BR'), inline: false },
                                { name: '🎁 Seus itens:', value: itemsToSend.map((item, index) => `${index + 1}. \`${item}\``).join('\n'), inline: false }
                            )
                            .setTimestamp()
                            .setFooter({ text: 'Obrigado pela compra!' });

                        await buyer.send({ embeds: [deliveryEmbed] });

                    } catch (dmError) {
                        console.error('Erro ao enviar DM:', dmError.message);
                    }
                }

                // Salvar dados atualizados do produto
                const ownerData = database.getUser(sellerId);
                if (ownerData && ownerData.products) {
                    const productIndex = ownerData.products.findIndex(p => p.id === productId);
                    if (productIndex !== -1) {
                        ownerData.products[productIndex] = product;
                        database.saveUser(sellerId, ownerData);
                    }
                }

                // Atualizar mensagem do produto no canal original se existir
                await this.updateProductMessage(sellerId, product);
            }

            // Obter canal do carrinho e mostrar confirmação
            const cartKey = `${buyerId}_${productId}`;
            const cartData2 = global.carts?.get(cartKey);

            if (cartData2) {
                // Atualizar log de pagamento
                await this.updatePaymentLog(cartData2, product, cartData, 'approved');

                const channel = this.client.channels.cache.get(cartData2.channelId);
                if (channel) {
                    // Limpar canal
                    try {
                        const messages = await channel.messages.fetch({ limit: 100 });
                        await channel.bulkDelete(messages);
                    } catch (error) {
                        console.log('Erro ao limpar canal:', error.message);
                    }

                    // Mostrar confirmação
                    const confirmationEmbed = new EmbedBuilder()
                        .setTitle('✅ Pagamento Confirmado!')
                        .setDescription('Sua compra foi processada com sucesso!')
                        .setColor(0x00ff00)
                        .addFields(
                            { name: '📦 Produto:', value: product.name, inline: true },
                            { name: '🛒 Quantidade:', value: cartData.quantity.toString(), inline: true },
                            { name: '💰 Valor pago:', value: `R$ ${(product.details.price * cartData.quantity).toFixed(2).replace('.', ',')}`, inline: true },
                            { name: '📬 Entrega:', value: 'Os itens foram enviados para sua DM', inline: false }
                        )
                        .setTimestamp();

                    await channel.send({ embeds: [confirmationEmbed] });

                    // Deletar canal após 15 segundos
                    setTimeout(async () => {
                        try {
                            await channel.delete('Compra finalizada');
                        } catch (error) {
                            console.log('Erro ao deletar canal:', error.message);
                        }
                    }, 15000);
                }

                // Remover carrinho da memória
                global.carts.delete(cartKey);
                console.log(`🧹 Carrinho ${cartKey} removido após compra finalizada`);
            }

            // Limpar também todos os pagamentos relacionados a este usuário e produto
            if (global.payments) {
                for (const [preferenceId, paymentDataItem] of global.payments.entries()) {
                    if (paymentDataItem.buyerId === buyerId && paymentDataItem.productId === productId) {
                        global.payments.delete(preferenceId);
                        console.log(`🧹 Pagamento ${preferenceId} removido após compra finalizada`);
                    }
                }
            }

        } catch (error) {
            console.error('Erro ao processar pagamento aprovado:', error.message);
        }
    }

    async updateProductMessage(sellerId, product) {
        try {
            // Buscar canal do produto
            if (!product.channelId || !product.embedMessageId) {
                return;
            }

            const channel = this.client.channels.cache.get(product.channelId);
            if (!channel) {
                return;
            }

            // Buscar mensagem do produto
            try {
                const message = await channel.messages.fetch(product.embedMessageId);
                if (!message) {
                    return;
                }

                // Criar embed atualizada
                const updatedEmbed = new EmbedBuilder(message.embeds[0].toJSON())
                    .setTitle(product.name);

                // Atualizar campo de estoque
                const fields = updatedEmbed.toJSON().fields || [];
                const stockFieldIndex = fields.findIndex(field => field.name.includes('Estoque') || field.name.includes('📦'));

                if (stockFieldIndex !== -1) {
                    fields[stockFieldIndex] = {
                        name: '📦 Estoque:',
                        value: product.details.infiniteStock ? '♾️ Infinito' : `${product.details.stock} unidades`,
                        inline: true
                    };
                    updatedEmbed.setFields(fields);
                }

                // Atualizar mensagem
                await message.edit({ embeds: [updatedEmbed] });
                console.log(`✅ Mensagem do produto ${product.name} atualizada com novo estoque: ${product.details.stock}`);

            } catch (messageError) {
                console.log('Erro ao atualizar mensagem do produto:', messageError.message);
            }

        } catch (error) {
            console.error('Erro ao atualizar mensagem do produto:', error.message);
        }
    }

    async updatePaymentLog(cartData, product, paymentData, status) {
        try {
            if (!cartData.logChannelId || !cartData.logMessageId) {
                return;
            }

            const logChannel = this.client.channels.cache.get(cartData.logChannelId);
            if (!logChannel) {
                return;
            }

            const logMessage = await logChannel.messages.fetch(cartData.logMessageId);
            if (!logMessage) {
                return;
            }

            const totalValue = product.details.price * paymentData.quantity;
            let statusText = '';
            let statusColor = 0xffa500;
            let showRefundButton = false;

            switch (status) {
                case 'approved':
                    statusText = '✅ Pagamento aprovado e produtos entregues';
                    statusColor = 0x00ff00;
                    showRefundButton = true;
                    break;
                case 'pending':
                    statusText = '⏳ Aguardando confirmação do pagamento';
                    statusColor = 0xffa500;
                    break;
                case 'rejected':
                    statusText = '❌ Pagamento rejeitado';
                    statusColor = 0xff0000;
                    break;
                case 'cancelled':
                    statusText = '🚫 Pagamento cancelado';
                    statusColor = 0xff0000;
                    break;
                case 'refunded':
                    statusText = '💸 Pagamento reembolsado';
                    statusColor = 0x9932cc;
                    break;
                case 'blocked_bank':
                    statusText = '🚫 Pagamento bloqueado (banco não permitido)';
                    statusColor = 0xff0000;
                    break;
                default:
                    statusText = `ℹ️ Status: ${status}`;
                    break;
            }

            // Atualizar embed do log
            const transactionId = cartData.transactionId || 'N/A';
            const updatedLogEmbed = new EmbedBuilder()
                .setTitle('📊 Log de Venda')
                .setDescription(`**ID da Transação:** \`${transactionId}\`\n\nHistórico completo desta transação.`)
                .setColor(statusColor)
                .addFields(
                    { name: '👤 Comprador:', value: `<@${cartData.buyerId}>`, inline: true },
                    { name: '🏪 Vendedor:', value: `<@${cartData.sellerId}>`, inline: true },
                    { name: '📦 Produto:', value: product.name, inline: true },
                    { name: '💰 Preço unitário:', value: `R$ ${product.details.price.toFixed(2).replace('.', ',')}`, inline: true },
                    { name: '🛒 Quantidade:', value: paymentData.quantity.toString(), inline: true },
                    { name: '💰 Valor total:', value: `R$ ${totalValue.toFixed(2).replace('.', ',')}`, inline: true },
                    { name: '📅 Data de criação:', value: new Date(cartData.createdAt).toLocaleString('pt-BR'), inline: false },
                    { name: '📅 Última atualização:', value: new Date().toLocaleString('pt-BR'), inline: false },
                    { name: '🔄 Status:', value: statusText, inline: false }
                )
                .setTimestamp()
                .setFooter({ text: `Sistema de Logs de Vendas • ID: ${transactionId}` });

            const components = [];
            if (showRefundButton) {
                const refundButton = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`refund_payment_${cartData.buyerId}_${cartData.productId}`)
                            .setLabel('Processar Reembolso')
                            .setStyle(ButtonStyle.Danger)
                            .setEmoji('💸')
                    );
                components.push(refundButton);
            }

            await logMessage.edit({
                embeds: [updatedLogEmbed],
                components: components
            });

        } catch (error) {
            console.error('Erro ao atualizar log de pagamento:', error.message);
        }
    }

    async updatePaymentLogWithBlockedBank(cartData, product, paymentData, bankInfo, payment, refundResult) {
        try {
            if (!cartData.logChannelId || !cartData.logMessageId) {
                return;
            }

            const logChannel = this.client.channels.cache.get(cartData.logChannelId);
            if (!logChannel) {
                return;
            }

            const logMessage = await logChannel.messages.fetch(cartData.logMessageId);
            if (!logMessage) {
                return;
            }

            const totalValue = product.details.price * paymentData.quantity;
            const transactionId = cartData.transactionId || 'N/A';

            // Atualizar embed do log com informações completas do bloqueio
            const updatedLogEmbed = new EmbedBuilder()
                .setTitle('📊 Log de Venda - Pagamento Bloqueado')
                .setDescription(`**ID da Transação:** \`${transactionId}\`\n\nPagamento foi automaticamente bloqueado e reembolsado pelo sistema de segurança.`)
                .setColor(0xff0000)
                .addFields(
                    { name: '👤 Comprador:', value: `<@${cartData.buyerId}>`, inline: true },
                    { name: '🏪 Vendedor:', value: `<@${cartData.sellerId}>`, inline: true },
                    { name: '📦 Produto:', value: product.name, inline: true },
                    { name: '💰 Preço unitário:', value: `R$ ${product.details.price.toFixed(2).replace('.', ',')}`, inline: true },
                    { name: '🛒 Quantidade:', value: paymentData.quantity.toString(), inline: true },
                    { name: '💰 Valor total:', value: `R$ ${totalValue.toFixed(2).replace('.', ',')}`, inline: true },
                    { name: '🏦 Banco Detectado:', value: bankInfo.bankName, inline: true },
                    { name: '🔍 Motivo do Bloqueio:', value: bankInfo.reason, inline: true },
                    { name: '🆔 ID do Pagamento:', value: payment.id.toString(), inline: true },
                    { name: '💸 Reembolso:', value: refundResult.success ? `✅ Processado (ID: ${refundResult.refundId})` : `❌ Erro: ${refundResult.error}`, inline: false },
                    { name: '📅 Data de criação:', value: new Date(cartData.createdAt).toLocaleString('pt-BR'), inline: true },
                    { name: '📅 Data do bloqueio:', value: new Date().toLocaleString('pt-BR'), inline: true },
                    { name: '🔄 Status:', value: '🚫 Pagamento bloqueado e reembolsado', inline: false }
                )
                .setTimestamp()
                .setFooter({ text: `Sistema de Proteção • ID: ${transactionId}` });

            await logMessage.edit({
                embeds: [updatedLogEmbed],
                components: [] // Remover botões pois não há necessidade de reembolso
            });

        } catch (error) {
            console.error('Erro ao atualizar log de pagamento bloqueado:', error.message);
        }
    }

    async deletePaymentLog(cartData, reason = 'Compra cancelada') {
        try {
            if (!cartData.logChannelId || !cartData.logMessageId) {
                return;
            }

            const logChannel = this.client.channels.cache.get(cartData.logChannelId);
            if (!logChannel) {
                return;
            }

            const logMessage = await logChannel.messages.fetch(cartData.logMessageId);
            if (!logMessage) {
                return;
            }

            // Deletar a mensagem do log
            await logMessage.delete();
            console.log(`🗑️ Log de pagamento deletado: ${cartData.transactionId || 'N/A'} - ${reason}`);

        } catch (error) {
            console.error('Erro ao deletar log de pagamento:', error.message);
        }
    }

    async handleCopyPix(interaction, preferenceId) {
        try {
            const paymentData = global.payments?.get(preferenceId);
            if (!paymentData || !paymentData.pixCode) {
                await interaction.reply({ content: '❌ Código PIX não encontrado.', ephemeral: true });
                return;
            }

            await interaction.reply({
                content: `📋 **Código PIX copiado!**\n\n\`\`\`${paymentData.pixCode}\`\`\`\n\n✅ Cole este código no seu app do banco para pagar.`,
                ephemeral: true
            });

        } catch (error) {
            console.error('Erro ao copiar PIX:', error.message);
            await interaction.reply({ content: '❌ Erro ao copiar código PIX.', ephemeral: true });
        }
    }

    async handleCheckPayment(interaction, preferenceId) {
        try {
            const paymentData = global.payments?.get(preferenceId);
            if (!paymentData) {
                await interaction.reply({ content: '❌ Dados do pagamento não encontrados.', ephemeral: true });
                return;
            }

            if (!paymentData.paymentId) {
                await interaction.reply({ content: '❌ ID do pagamento não encontrado.', ephemeral: true });
                return;
            }

            const { sellerId } = paymentData;
            const sellerCredentials = this.getUserCredentials(sellerId);

            // Verificar status do pagamento
            try {
                const response = await axios.get(`https://api.mercadopago.com/v1/payments/${paymentData.paymentId}`, {
                    headers: {
                        'Authorization': `Bearer ${sellerCredentials.accessToken}`
                    }
                });

                const payment = response.data;
                let statusMessage = '';
                let statusColor = 0xffa500;

                switch (payment.status) {
                    case 'approved':
                        statusMessage = '✅ **Pagamento Aprovado!**\nSeu pagamento foi confirmado. Os itens serão entregues em breve.';
                        statusColor = 0x00ff00;
                        break;
                    case 'pending':
                        statusMessage = '⏳ **Pagamento Pendente**\nAguardando confirmação do pagamento. Isso pode levar alguns minutos.';
                        statusColor = 0xffa500;
                        break;
                    case 'in_process':
                        statusMessage = '🔄 **Pagamento em Processamento**\nSeu pagamento está sendo processado.';
                        statusColor = 0x0099ff;
                        break;
                    case 'rejected':
                        statusMessage = '❌ **Pagamento Rejeitado**\nSeu pagamento foi rejeitado. Tente novamente.';
                        statusColor = 0xff0000;
                        break;
                    case 'cancelled':
                        statusMessage = '🚫 **Pagamento Cancelado**\nO pagamento foi cancelado.';
                        statusColor = 0xff0000;
                        break;
                    default:
                        statusMessage = `ℹ️ **Status:** ${payment.status}`;
                        break;
                }

                const statusEmbed = new EmbedBuilder()
                    .setTitle('🔍 Status do Pagamento')
                    .setDescription(statusMessage)
                    .setColor(statusColor)
                    .addFields(
                        { name: '🆔 ID do Pagamento:', value: payment.id.toString(), inline: true },
                        { name: '💰 Valor:', value: `R$ ${payment.transaction_amount.toFixed(2).replace('.', ',')}`, inline: true },
                        { name: '📅 Data:', value: new Date(payment.date_created).toLocaleString('pt-BR'), inline: true }
                    )
                    .setTimestamp();

                await interaction.reply({ embeds: [statusEmbed], ephemeral: true });

            } catch (mpError) {
                console.error('Erro ao verificar pagamento:', mpError.response?.data || mpError.message);
                await interaction.reply({
                    content: '❌ Erro ao verificar status do pagamento. Tente novamente.',
                    ephemeral: true
                });
            }

        } catch (error) {
            console.error('Erro ao verificar pagamento:', error.message);
            await interaction.reply({ content: '❌ Erro ao verificar pagamento.', ephemeral: true });
        }
    }

    // Método para limpeza automática de carrinhos órfãos
    async cleanupOrphanedCarts() {
        if (!global.carts) return;

        const cartsToRemove = [];

        for (const [cartKey, cartData] of global.carts.entries()) {
            // Verificar se o canal ainda existe
            const channel = this.client.channels.cache.get(cartData.channelId);
            if (!channel) {
                cartsToRemove.push(cartKey);
            }
        }

        // Remover carrinhos órfãos
        for (const cartKey of cartsToRemove) {
            const cartData = global.carts.get(cartKey);
            if (cartData) {
                await this.deletePaymentLog(cartData, 'Carrinho órfão removido automaticamente');
            }
            global.carts.delete(cartKey);
            console.log(`🧹 Carrinho órfão removido: ${cartKey}`);
        }

        if (cartsToRemove.length > 0) {
            console.log(`🧹 Limpeza concluída: ${cartsToRemove.length} carrinhos órfãos removidos`);
        }
    }

    // Método para verificar se um usuário pode criar um novo carrinho
    async canUserCreateCart(buyerId) {
        if (!global.carts) {
            global.carts = new Map();
            return true;
        }

        // Primeiro, limpar carrinhos órfãos
        await this.cleanupOrphanedCarts();

        // Verificar se o usuário tem carrinho ativo
        for (const [, cartData] of global.carts.entries()) {
            if (cartData.buyerId === buyerId) {
                return false;
            }
        }

        return true;
    }

    async handleConfigProductImport(interaction, productId) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const product = userData.products.find(p => p.id === productId);

        if (!product) {
            await interaction.reply({ content: '❌ Produto não encontrado.', ephemeral: true });
            return;
        }

        try {
            const modal = new ModalBuilder()
                .setCustomId(`config_product_import_modal_${productId}`)
                .setTitle('Importar Configurações');

            const importInput = new TextInputBuilder()
                .setCustomId('product_import_code_input')
                .setLabel('Código de Importação')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('Ex: ABCD-EFGH-IJKL')
                .setRequired(true);

            const instructionsInput = new TextInputBuilder()
                .setCustomId('product_import_instructions_input')
                .setLabel('Instruções (apenas leitura)')
                .setStyle(TextInputStyle.Paragraph)
                .setValue('Cole aqui o código de importação que você recebeu.\n\nFormato: XXXX-XXXX-XXXX\n\nO código importará todas as configurações do produto original (descrição, preço, botão de compra, etc.)')
                .setRequired(false);

            modal.addComponents(
                new ActionRowBuilder().addComponents(instructionsInput),
                new ActionRowBuilder().addComponents(importInput)
            );
            await interaction.showModal(modal);
        } catch (error) {
            console.error('Erro ao mostrar modal de importação:', error.message);
        }
    }

    async handleImportProductConfig(interaction, productId, importCode) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const product = userData.products.find(p => p.id === productId);

        if (!product) {
            await interaction.reply({ content: '❌ Produto não encontrado.', ephemeral: true });
            return;
        }

        // Validar formato do código
        if (!/^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/.test(importCode)) {
            await interaction.reply({
                content: '❌ **Código inválido!**\n\nO código deve ter o formato: **XXXX-XXXX-XXXX**\n\nExemplo: `ABCD-EFGH-IJKL`',
                ephemeral: true
            });
            return;
        }

        // Verificar se o código existe
        if (!global.exportedConfigs || !global.exportedConfigs.has(importCode)) {
            await interaction.reply({
                content: '❌ **Código não encontrado!**\n\nVerifique se:\n• O código foi digitado corretamente\n• O código não expirou (válido por 30 dias)\n• O código existe',
                ephemeral: true
            });
            return;
        }

        try {
            const configData = global.exportedConfigs.get(importCode);

            // Verificar se não expirou
            if (new Date() > new Date(configData.expiresAt)) {
                await interaction.reply({
                    content: '❌ **Código expirado!**\n\nEste código de importação expirou. Códigos são válidos por 30 dias.',
                    ephemeral: true
                });
                return;
            }

            const importData = configData.data;

            // Importar configurações (exceto nome e estoque)
            product.details.description = importData.details.description || product.details.description;
            product.details.price = importData.details.price || product.details.price;
            product.details.thumbnail = importData.details.thumbnail || product.details.thumbnail;
            product.details.banner = importData.details.banner || product.details.banner;
            product.details.color = importData.details.color || product.details.color;
            product.details.buyButtonText = importData.details.buyButtonText || product.details.buyButtonText;
            product.details.buyButtonEmoji = importData.details.buyButtonEmoji || product.details.buyButtonEmoji;
            product.details.buyButtonStyle = importData.details.buyButtonStyle || product.details.buyButtonStyle;
            product.details.buyButtonColor = importData.details.buyButtonColor || product.details.buyButtonColor;
            product.details.roleId = importData.details.roleId || product.details.roleId;

            database.saveUser(userId, userData);

            // Atualizar a embed de configuração
            const channel = interaction.guild.channels.cache.get(product.channelId);
            if (channel && product.embedMessageId) {
                try {
                    const message = await channel.messages.fetch(product.embedMessageId);
                    await this.updateProductConfigurationEmbed(message, product);
                } catch (error) {
                    console.error('Erro ao atualizar embed:', error.message);
                }
            }

            await interaction.reply({
                content: `✅ **Configurações importadas com sucesso!**\n\n🔑 **Código:** \`${importCode}\`\n📦 **De:** ${importData.productName}\n📦 **Para:** ${product.name}\n📅 **Data:** ${new Date().toLocaleString('pt-BR')}\n\n🔄 **Configurações aplicadas:**\n• Descrição, preço, imagens\n• Botão de compra personalizado\n• Cargo e outras configurações`,
                ephemeral: true
            });
        } catch (error) {
            console.error('Erro ao importar configurações:', error.message);
            await interaction.reply({ content: '❌ Erro ao importar configurações.', ephemeral: true });
        }
    }

    async updateProductConfigurationEmbed(message, product) {
        // Recriar a embed de configuração atualizada
        const configEmbed = new EmbedBuilder()
            .setTitle(`🛠️ Configuração do Produto: ${product.name}`)
            .setDescription('Configure todos os aspectos do seu produto usando os botões abaixo:')
            .setColor(product.details.color || 0x0099ff)
            .addFields(
                { name: '📝 Nome', value: product.name, inline: true },
                { name: '📄 Descrição', value: product.details.description, inline: true },
                { name: '💰 Preço', value: `R$ ${product.details.price.toFixed(2).replace('.', ',')}`, inline: true },
                { name: '🖼️ Miniatura', value: product.details.thumbnail ? 'Configurada' : 'Não configurada', inline: true },
                { name: '🎨 Banner', value: product.details.banner ? 'Configurado' : 'Não configurado', inline: true },
                { name: '📦 Estoque', value: product.details.infiniteStock ? '♾️ Infinito' : `${product.details.stock} unidades`, inline: true },
                { name: '🛒 Botão de Compra', value: `${product.details.buyButtonEmoji || '🛒'} ${product.details.buyButtonText || 'Comprar'}`, inline: true },
                { name: '👥 Cargo', value: product.details.roleId ? `<@&${product.details.roleId}>` : 'Não configurado', inline: true }
            )
            .setFooter({ text: 'Use os botões abaixo para configurar cada aspecto do produto' })
            .setTimestamp();

        // Se houver miniatura configurada, adicionar à embed
        if (product.details.thumbnail) {
            configEmbed.setThumbnail(product.details.thumbnail);
        }

        // Se houver banner configurado, adicionar à embed
        if (product.details.banner) {
            configEmbed.setImage(product.details.banner);
        }

        // Manter os mesmos componentes
        await message.edit({ embeds: [configEmbed] });
    }

    async handleUpdateProductDetails(interaction, productId, field, newValue) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const product = userData.products.find(p => p.id === productId);

        if (!product) {
            await interaction.reply({ content: 'Produto não encontrado.', ephemeral: true });
            return;
        }

        // Atualizar o campo específico
        if (field === 'name') {
            product.name = newValue;
        } else {
            product.details[field] = newValue;
        }

        database.saveUser(userId, userData);

        // Atualizar a mensagem embed no canal do produto
        try {
            const guild = interaction.guild;
            if (!guild) {
                throw new Error('Não foi possível acessar o servidor.');
            }

            const channel = guild.channels.cache.get(product.channelId);
            if (channel && product.embedMessageId) {
                const message = await channel.messages.fetch(product.embedMessageId);
                if (message) {
                    const updatedEmbed = new EmbedBuilder(message.embeds[0].toJSON())
                        .setTitle(product.name);

                    if (field === 'description') {
                        updatedEmbed.setDescription(newValue);
                    } else if (field === 'image') {
                        updatedEmbed.setImage(newValue);
                    } else if (field === 'price') {
                        // Encontrar e atualizar o campo de preço
                        const priceField = updatedEmbed.data.fields.find(f => f.name === 'Preço');
                        if (priceField) priceField.value = `R$ ${newValue.toFixed(2).replace('.', ',')}`;
                        else updatedEmbed.addFields({ name: 'Preço', value: `R$ ${newValue.toFixed(2).replace('.', ',')}`, inline: true });
                    } else if (field === 'stock') {
                        // Encontrar e atualizar o campo de estoque
                        const stockField = updatedEmbed.data.fields.find(f => f.name === 'Estoque');
                        if (stockField) stockField.value = newValue.toString();
                        else updatedEmbed.addFields({ name: 'Estoque', value: newValue.toString(), inline: true });
                    }

                    await message.edit({ embeds: [updatedEmbed] });
                }
            }

            await interaction.reply({ content: `Produto atualizado com sucesso!`, ephemeral: true });
        } catch (error) {
            console.error(`Erro ao atualizar embed do produto: ${error.message}`);
            await interaction.reply({ content: `Erro ao atualizar o produto no Discord: ${error.message}`, ephemeral: true });
        }
    }

    async handleShowProductEditOptions(interaction, productId) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const product = userData.products?.find(p => p.id === productId);

        if (!product) {
            await interaction.update({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('❌ Produto não encontrado')
                        .setDescription('O produto selecionado não foi encontrado.')
                        .setColor(0xff0000)
                ],
                components: []
            });

            setTimeout(async () => {
                await this.handleGerenciarProdutos(interaction);
            }, 2000);
            return;
        }

        // Encontrar a categoria do produto
        const category = userData.categories?.find(cat => cat.id === product.categoryId);
        const categoryName = category ? category.name : 'Categoria não encontrada';

        const embed = new EmbedBuilder()
            .setTitle(`✏️ Editar Produto: ${product.name}`)
            .setDescription('Selecione o que deseja editar neste produto:')
            .setColor(0x2196f3)
            .addFields(
                {
                    name: '📋 Informações Atuais:',
                    value: `• **Nome:** ${product.name}\n• **Preço:** R$ ${product.details.price.toFixed(2).replace('.', ',')}\n• **Estoque:** ${product.details.stock} unidades\n• **Categoria:** ${categoryName}`,
                    inline: false
                },
                {
                    name: '📝 Descrição:',
                    value: product.details.description.length > 100 ?
                        product.details.description.substring(0, 100) + '...' :
                        product.details.description,
                    inline: false
                }
            )
            .setFooter({ text: 'Selecione uma opção para editar' })
            .setTimestamp();

        // Adicionar imagem se existir
        if (product.details.image && product.details.image !== 'https://via.placeholder.com/400x300/2196f3/ffffff?text=Produto') {
            embed.setThumbnail(product.details.image);
        }

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`edit_product_name_${productId}`)
                    .setLabel('Nome')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('✏️'),
                new ButtonBuilder()
                    .setCustomId(`edit_product_description_${productId}`)
                    .setLabel('Descrição')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📝'),
                new ButtonBuilder()
                    .setCustomId(`edit_product_price_${productId}`)
                    .setLabel('Preço')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('💰'),
                new ButtonBuilder()
                    .setCustomId(`edit_product_stock_${productId}`)
                    .setLabel('Estoque')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📦')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`edit_product_image_${productId}`)
                    .setLabel('Imagem')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🖼️'),
                new ButtonBuilder()
                    .setCustomId(`edit_product_category_${productId}`)
                    .setLabel('Categoria')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📂'),
                new ButtonBuilder()
                    .setCustomId('editar_produto')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });
    }

    async handleConfirmProductRemoval(interaction, productId) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const product = userData.products?.find(p => p.id === productId);

        if (!product) {
            await interaction.update({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('❌ Produto não encontrado')
                        .setDescription('O produto selecionado não foi encontrado.')
                        .setColor(0xff0000)
                ],
                components: []
            });

            setTimeout(async () => {
                await this.handleGerenciarProdutos(interaction);
            }, 2000);
            return;
        }

        // Encontrar a categoria do produto
        const category = userData.categories?.find(cat => cat.id === product.categoryId);
        const categoryName = category ? category.name : 'Categoria não encontrada';

        const embed = new EmbedBuilder()
            .setTitle('⚠️ Confirmar Remoção de Produto')
            .setDescription(`Você está prestes a remover o produto **${product.name}**`)
            .setColor(0xff0000)
            .addFields(
                {
                    name: '📊 Informações do produto:',
                    value: `• **Nome:** ${product.name}\n• **Preço:** R$ ${product.details.price.toFixed(2).replace('.', ',')}\n• **Estoque:** ${product.details.stock} unidades\n• **Categoria:** ${categoryName}\n• **Criado em:** ${new Date(product.createdAt).toLocaleDateString('pt-BR')}`,
                    inline: false
                },
                {
                    name: '⚠️ Atenção:',
                    value: 'Esta ação irá remover permanentemente:\n• O produto e todas suas configurações\n• O canal do Discord associado\n• Histórico de vendas deste produto\n\n**Esta ação não pode ser desfeita!**',
                    inline: false
                }
            )
            .setFooter({ text: 'Confirme apenas se tem certeza absoluta' })
            .setTimestamp();

        // Adicionar imagem se existir
        if (product.details.image && product.details.image !== 'https://via.placeholder.com/400x300/2196f3/ffffff?text=Produto') {
            embed.setThumbnail(product.details.image);
        }

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`confirm_remove_product_${productId}`)
                    .setLabel('Confirmar Remoção')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('⚠️'),
                new ButtonBuilder()
                    .setCustomId('remover_produto')
                    .setLabel('Cancelar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }

    async handleExecuteProductRemoval(interaction, productId) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        const productIndex = userData.products?.findIndex(p => p.id === productId);

        if (productIndex === -1 || !userData.products) {
            await interaction.update({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('❌ Produto não encontrado')
                        .setDescription('O produto selecionado não foi encontrado.')
                        .setColor(0xff0000)
                ],
                components: []
            });

            setTimeout(async () => {
                await this.handleGerenciarProdutos(interaction);
            }, 2000);
            return;
        }

        const product = userData.products[productIndex];
        const productName = product.name;

        // Mostrar mensagem de processamento
        await interaction.update({
            embeds: [
                new EmbedBuilder()
                    .setTitle('⏳ Removendo produto...')
                    .setDescription(`Removendo o produto **${productName}**. Por favor, aguarde...`)
                    .setColor(0xffa500)
            ],
            components: []
        });

        try {
            // Remover canal do Discord se existir
            if (product.channelId) {
                try {
                    const guild = interaction.guild;
                    if (guild) {
                        const channel = await guild.channels.fetch(product.channelId).catch(() => null);
                        if (channel) {
                            await channel.delete('Produto removido pelo usuário');
                        }
                    }
                } catch (error) {
                    console.error(`Erro ao excluir canal do produto: ${error.message}`);
                    // Continuar mesmo se falhar a exclusão do canal
                }
            }

            // Remover produto da categoria
            if (product.categoryId && userData.categories) {
                const category = userData.categories.find(cat => cat.id === product.categoryId);
                if (category && category.products) {
                    const productIndexInCategory = category.products.indexOf(productId);
                    if (productIndexInCategory > -1) {
                        category.products.splice(productIndexInCategory, 1);
                    }
                }
            }

            // Remover produto dos dados do usuário
            userData.products.splice(productIndex, 1);

            // Salvar dados atualizados
            database.saveUser(userId, userData);

            // Mostrar mensagem de sucesso
            await interaction.editReply({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('✅ Produto removido com sucesso!')
                        .setDescription(`O produto **${productName}** foi removido com sucesso.\n\n📊 **Estatísticas atualizadas:**\n• Total de produtos: ${userData.products.length}\n• Canal do Discord: Removido\n• Dados: Limpos`)
                        .setColor(0x00ff00)
                ],
                components: []
            });

            // Voltar para a tela de gerenciamento de produtos após 3 segundos
            setTimeout(async () => {
                await this.handleGerenciarProdutos(interaction);
            }, 3000);

        } catch (error) {
            console.error(`Erro ao remover produto: ${error.message}`);

            // Mostrar mensagem de erro
            await interaction.editReply({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('❌ Erro ao remover produto')
                        .setDescription(`Ocorreu um erro ao remover o produto: ${error.message}`)
                        .setColor(0xff0000)
                ],
                components: []
            });

            // Voltar para a tela de gerenciamento de produtos após 3 segundos
            setTimeout(async () => {
                await this.handleGerenciarProdutos(interaction);
            }, 3000);
        }
    }

    async handleConfirmarRemoverCategoria(interaction, categoryId) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        
        // Verificar se o usuário tem categorias
        if (!userData.categories || userData.categories.length === 0) {
            await interaction.update({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('❌ Erro')
                        .setDescription('Você não possui categorias para remover.')
                        .setColor(0xff0000)
                ],
                components: []
            });
            
            setTimeout(async () => {
                await this.handleGerenciarCategorias(interaction);
            }, 2000);
            return;
        }
        
        // Encontrar a categoria pelo ID
        const categoryIndex = userData.categories.findIndex(cat => cat.id === categoryId);
        if (categoryIndex === -1) {
            await interaction.update({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('❌ Erro')
                        .setDescription('Categoria não encontrada.')
                        .setColor(0xff0000)
                ],
                components: []
            });
            
            setTimeout(async () => {
                await this.handleGerenciarCategorias(interaction);
            }, 2000);
            return;
        }
        
        const category = userData.categories[categoryIndex];
        const categoryName = category.name;
        const discordCategoryId = category.discordCategoryId;
        
        // Mostrar mensagem de processamento
        await interaction.update({
            embeds: [
                new EmbedBuilder()
                    .setTitle('⏳ Removendo categoria...')
                    .setDescription(`Removendo a categoria **${categoryName}**. Por favor, aguarde...`)
                    .setColor(0xffa500)
            ],
            components: []
        });
        
        try {
            // Remover a categoria do Discord se existir
            if (discordCategoryId) {
                try {
                    const guild = interaction.guild;
                    if (guild) {
                        const discordCategory = await guild.channels.fetch(discordCategoryId).catch(() => null);
                        if (discordCategory) {
                            await discordCategory.delete('Categoria removida pelo usuário');
                        }
                    }
                } catch (error) {
                    console.error(`Erro ao excluir categoria do Discord: ${error.message}`);
                    // Continuar mesmo se falhar a exclusão no Discord
                }
            }
            
            // Remover a categoria dos dados do usuário
            userData.categories.splice(categoryIndex, 1);
            
            // Salvar dados atualizados
            database.saveUser(userId, userData);
            
            // Mostrar mensagem de sucesso
            await interaction.editReply({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('✅ Categoria removida com sucesso!')
                        .setDescription(`A categoria **${categoryName}** foi removida com sucesso.`)
                        .setColor(0x00ff00)
                ],
                components: []
            });
            
            // Voltar para a tela de gerenciamento de categorias após 2 segundos
            setTimeout(async () => {
                await this.handleGerenciarCategorias(interaction);
            }, 2000);
            
        } catch (error) {
            console.error(`Erro ao remover categoria: ${error.message}`);
            
            // Mostrar mensagem de erro
            await interaction.editReply({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('❌ Erro ao remover categoria')
                        .setDescription(`Ocorreu um erro ao remover a categoria: ${error.message}`)
                        .setColor(0xff0000)
                ],
                components: []
            });
            
            // Voltar para a tela de gerenciamento de categorias após 3 segundos
            setTimeout(async () => {
                await this.handleGerenciarCategorias(interaction);
            }, 3000);
        }
    }
    
    async handleCriarNovaCategoria(interaction) {
        // Criar modal para entrada do nome da categoria
        const modal = new ModalBuilder()
            .setCustomId('criar_categoria_modal')
            .setTitle('Criar Nova Categoria');
            
        const categoryNameInput = new TextInputBuilder()
            .setCustomId('category_name_input')
            .setLabel('Nome da categoria')
            .setPlaceholder('Ex: Cursos, E-books, Serviços...')
            .setStyle(TextInputStyle.Short)
            .setMinLength(3)
            .setMaxLength(50)
            .setRequired(true);
            
        const firstActionRow = new ActionRowBuilder().addComponents(categoryNameInput);
        modal.addComponents(firstActionRow);
        
        await interaction.showModal(modal);
    }

    async handleAdicionarProduto(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('📦 Adicionar Produto')
            .setDescription('Adicione um novo produto à sua categoria selecionada:')
            .setColor(0x5865f2)
            .addFields(
                {
                    name: '📋 Informações do produto:',
                    value: '• **Nome do produto**\n• **Descrição detalhada**\n• **Preço**\n• **Categoria**\n• **Arquivo/conteúdo digital**',
                    inline: false
                },
                {
                    name: '💡 Dicas para um bom produto:',
                    value: '• Use nomes claros e atrativos\n• Descreva todos os benefícios\n• Defina preços competitivos\n• Organize em categorias lógicas',
                    inline: false
                },
                {
                    name: '⚡ Entrega automática:',
                    value: 'Após o pagamento, o produto será entregue automaticamente via DM para o cliente.',
                    inline: false
                }
            )
            .setFooter({ text: 'Clique em "Criar Produto" para começar' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('create_product')
                    .setLabel('Criar Novo Produto')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('📦'),
                new ButtonBuilder()
                    .setCustomId('list_products')
                    .setLabel('Ver Produtos Existentes')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('📋'),
                new ButtonBuilder()
                    .setCustomId('back_to_config')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }

    async handleAtivarCartao(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('💳 Ativar Pagamento com Cartão')
            .setDescription('Configure pagamentos com cartão de crédito via Checkout Pro do Mercado Pago:')
            .setColor(0x00d4aa)
            .addFields(
                {
                    name: '🛒 Checkout Pro - Mercado Pago:',
                    value: 'Permite que seus clientes paguem com cartão de crédito, débito, PIX e boleto em uma interface segura do Mercado Pago.',
                    inline: false
                },
                {
                    name: '✅ Vantagens:',
                    value: '• Aceita todos os cartões principais\n• Interface segura e confiável\n• Parcelamento automático\n• Proteção contra fraudes\n• Aprovação instantânea',
                    inline: false
                },
                {
                    name: '⚠️ Requisitos:',
                    value: '• Conta do Mercado Pago configurada\n• Access Token e Client Secret válidos\n• Conta aprovada para receber pagamentos',
                    inline: false
                }
            )
            .setFooter({ text: 'Configure primeiro sua conta do Mercado Pago' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('enable_checkout_pro')
                    .setLabel('Ativar Checkout Pro')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('💳'),
                new ButtonBuilder()
                    .setCustomId('auth_mercado_pago')
                    .setLabel('Configurar Mercado Pago')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🛒'),
                new ButtonBuilder()
                    .setCustomId('back_to_config')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }

    async handleAtivarPixTransparente(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('⚡ Ativar Pagamento PIX Transparente')
            .setDescription('Configure pagamentos apenas com PIX via Checkout Transparente do Mercado Pago:')
            .setColor(0x00ff88)
            .addFields(
                {
                    name: '⚡ Checkout Transparente - PIX:',
                    value: 'Permite que seus clientes paguem apenas com PIX, sem sair do Discord, com QR Code gerado automaticamente.',
                    inline: false
                },
                {
                    name: '✅ Vantagens:',
                    value: '• Pagamento instantâneo 24/7\n• Sem taxas para o cliente\n• QR Code automático\n• Confirmação em tempo real\n• Maior conversão',
                    inline: false
                },
                {
                    name: '⚠️ Requisitos:',
                    value: '• Conta do Mercado Pago configurada\n• Access Token e Client Secret válidos\n• Chave PIX cadastrada no MP',
                    inline: false
                }
            )
            .setFooter({ text: 'Configure primeiro sua conta do Mercado Pago' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('enable_pix_transparent')
                    .setLabel('Ativar PIX Transparente')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('⚡'),
                new ButtonBuilder()
                    .setCustomId('auth_mercado_pago')
                    .setLabel('Configurar Mercado Pago')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🛒'),
                new ButtonBuilder()
                    .setCustomId('back_to_config')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }

    async handleConfigurarAPI(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('🧰 Configurar API (Para Desenvolvedores)')
            .setDescription('Configure webhooks e APIs para integrar com seus sistemas:')
            .setColor(0x7289da)
            .addFields(
                {
                    name: '🔗 Webhooks disponíveis:',
                    value: '• **Pagamento aprovado** - Notifica quando um pagamento é confirmado\n• **Novo cliente** - Notifica quando um novo cliente se cadastra\n• **Produto vendido** - Notifica sobre cada venda realizada',
                    inline: false
                },
                {
                    name: '📡 Formato das requisições:',
                    value: '• **Método:** POST\n• **Content-Type:** application/json\n• **Autenticação:** Bearer Token (opcional)',
                    inline: false
                },
                {
                    name: '⚠️ Importante:',
                    value: 'Esta funcionalidade é destinada a desenvolvedores. Certifique-se de ter conhecimento técnico antes de configurar.',
                    inline: false
                }
            )
            .setFooter({ text: 'Configure apenas se você souber o que está fazendo' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_webhooks')
                    .setLabel('Configurar Webhooks')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🔗'),
                new ButtonBuilder()
                    .setCustomId('api_documentation')
                    .setLabel('Ver Documentação')
                    .setStyle(ButtonStyle.Link)
                    .setURL('https://docs.nodexsolutions.com/api')
                    .setEmoji('📚'),
                new ButtonBuilder()
                    .setCustomId('back_to_config')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }

    // ===== HANDLERS DAS CATEGORIAS PRINCIPAIS =====

    async handleConfigurarPagamentos(interaction) {
        const userId = interaction.user.id;
        const { embed, components } = this.buildPagamentosContent(userId);
        await interaction.update({ embeds: [embed], components });
    }

    async handleConfigurarLoja(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('🏪 Configurar Loja')
            .setDescription('Gerencie todos os aspectos da sua loja virtual:')
            .setColor(0x7289da)
            .addFields(
                {
                    name: '📦 Produtos',
                    value: 'Adicionar, editar e gerenciar produtos',
                    inline: true
                },
                {
                    name: '📂 Categorias',
                    value: 'Organizar produtos em categorias',
                    inline: true
                },
                {
                    name: '💰 Preços',
                    value: 'Definir preços e promoções',
                    inline: true
                },
                {
                    name: '📋 Estoque',
                    value: 'Controlar quantidade e disponibilidade',
                    inline: true
                },
                {
                    name: '🎁 Cupons',
                    value: 'Criar cupons de desconto',
                    inline: true
                },
                {
                    name: '📊 Vendas',
                    value: 'Histórico e relatórios de vendas',
                    inline: true
                }
            )
            .setFooter({ text: 'Selecione uma opção para gerenciar sua loja' })
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('gerenciar_produtos')
                    .setLabel('Gerenciar Produtos')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('📦'),
                new ButtonBuilder()
                    .setCustomId('gerenciar_categorias')
                    .setLabel('Gerenciar Categorias')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('📂'),
                new ButtonBuilder()
                    .setCustomId('configurar_precos')
                    .setLabel('Configurar Preços')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('💰')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('controlar_estoque')
                    .setLabel('Controlar Estoque')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📋'),
                new ButtonBuilder()
                    .setCustomId('criar_cupons')
                    .setLabel('Criar Cupons')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🎁'),
                new ButtonBuilder()
                    .setCustomId('historico_vendas')
                    .setLabel('Histórico de Vendas')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📊')
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('back_to_config')
                    .setLabel('Voltar ao Menu')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row1, row2, row3] });
    }

    async handlePersonalizarSistema(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('🎨 Personalizar Sistema')
            .setDescription('Customize a aparência e comportamento do seu bot:')
            .setColor(0xe91e63)
            .addFields(
                {
                    name: '🎨 Cores e Temas',
                    value: 'Personalize cores das embeds e temas',
                    inline: true
                },
                {
                    name: '💬 Mensagens',
                    value: 'Customize mensagens automáticas',
                    inline: true
                },
                {
                    name: '🔘 Botões',
                    value: 'Altere textos e estilos dos botões',
                    inline: true
                },
                {
                    name: '🖼️ Imagens',
                    value: 'Configure logos e imagens do bot',
                    inline: true
                },
                {
                    name: '📝 Textos',
                    value: 'Personalize descrições e textos',
                    inline: true
                },
                {
                    name: '⚙️ Comportamento',
                    value: 'Configure ações e respostas automáticas',
                    inline: true
                }
            )
            .setFooter({ text: 'Personalize seu bot do seu jeito' })
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('personalizar_cores')
                    .setLabel('Cores e Temas')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🎨'),
                new ButtonBuilder()
                    .setCustomId('personalizar_mensagens')
                    .setLabel('Mensagens')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('💬'),
                new ButtonBuilder()
                    .setCustomId('personalizar_botoes')
                    .setLabel('Botões')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🔘')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('personalizar_imagens')
                    .setLabel('Imagens e Logos')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🖼️'),
                new ButtonBuilder()
                    .setCustomId('personalizar_textos')
                    .setLabel('Textos')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📝'),
                new ButtonBuilder()
                    .setCustomId('personalizar_comportamento')
                    .setLabel('Comportamento')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⚙️')
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('back_to_config')
                    .setLabel('Voltar ao Menu')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row1, row2, row3] });
    }

    async handleConfiguracoesAvancadas(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('🔧 Configurações Avançadas')
            .setDescription('Configurações técnicas e integrações para desenvolvedores:')
            .setColor(0x607d8b)
            .addFields(
                {
                    name: '🔗 APIs e Webhooks',
                    value: 'Configure integrações externas',
                    inline: true
                },
                {
                    name: '🔐 Segurança',
                    value: 'Configurações de segurança avançadas',
                    inline: true
                },
                {
                    name: '📡 Integrações',
                    value: 'Conecte com outros sistemas',
                    inline: true
                },
                {
                    name: '🗄️ Banco de Dados',
                    value: 'Configurações de armazenamento',
                    inline: true
                },
                {
                    name: '📊 Logs e Monitoramento',
                    value: 'Sistema de logs e monitoramento',
                    inline: true
                },
                {
                    name: '🔄 Backup e Restore',
                    value: 'Backup automático de dados',
                    inline: true
                }
            )
            .setFooter({ text: 'Configurações para usuários avançados' })
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('configurar_apis')
                    .setLabel('APIs e Webhooks')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🔗'),
                new ButtonBuilder()
                    .setCustomId('configurar_seguranca')
                    .setLabel('Segurança')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🔐'),
                new ButtonBuilder()
                    .setCustomId('configurar_integracoes')
                    .setLabel('Integrações')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('📡')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('configurar_database')
                    .setLabel('Banco de Dados')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🗄️'),
                new ButtonBuilder()
                    .setCustomId('configurar_logs')
                    .setLabel('Logs e Monitoramento')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📊'),
                new ButtonBuilder()
                    .setCustomId('configurar_backup')
                    .setLabel('Backup e Restore')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔄')
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('back_to_config')
                    .setLabel('Voltar ao Menu')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row1, row2, row3] });
    }

    async handleRelatoriosAnalytics(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('📊 Relatórios e Analytics')
            .setDescription('Visualize estatísticas detalhadas e relatórios de performance:')
            .setColor(0x4caf50)
            .addFields(
                {
                    name: '💰 Vendas',
                    value: 'Relatórios de vendas e faturamento',
                    inline: true
                },
                {
                    name: '👥 Clientes',
                    value: 'Análise de comportamento de clientes',
                    inline: true
                },
                {
                    name: '📈 Performance',
                    value: 'Métricas de performance do bot',
                    inline: true
                },
                {
                    name: '🎯 Conversões',
                    value: 'Taxa de conversão e funil de vendas',
                    inline: true
                },
                {
                    name: '📦 Produtos',
                    value: 'Produtos mais vendidos e populares',
                    inline: true
                },
                {
                    name: '📅 Períodos',
                    value: 'Relatórios por período personalizado',
                    inline: true
                }
            )
            .setFooter({ text: 'Analise o desempenho da sua loja' })
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('relatorio_vendas')
                    .setLabel('Relatório de Vendas')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('💰'),
                new ButtonBuilder()
                    .setCustomId('analise_clientes')
                    .setLabel('Análise de Clientes')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('👥'),
                new ButtonBuilder()
                    .setCustomId('metricas_performance')
                    .setLabel('Métricas de Performance')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('📈')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('taxa_conversao')
                    .setLabel('Taxa de Conversão')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🎯'),
                new ButtonBuilder()
                    .setCustomId('produtos_populares')
                    .setLabel('Produtos Populares')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📦'),
                new ButtonBuilder()
                    .setCustomId('relatorio_personalizado')
                    .setLabel('Relatório Personalizado')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📅')
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('back_to_config')
                    .setLabel('Voltar ao Menu')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row1, row2, row3] });
    }

    async handleSuporteAjuda(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('🆘 Suporte e Ajuda')
            .setDescription('Acesse documentação, tutoriais e canais de suporte:')
            .setColor(0xff9800)
            .addFields(
                {
                    name: '📚 Documentação',
                    value: 'Guias completos e documentação técnica',
                    inline: true
                },
                {
                    name: '🎥 Tutoriais',
                    value: 'Vídeos e tutoriais passo a passo',
                    inline: true
                },
                {
                    name: '💬 Suporte Técnico',
                    value: 'Chat direto com nossa equipe',
                    inline: true
                },
                {
                    name: '❓ FAQ',
                    value: 'Perguntas frequentes e respostas',
                    inline: true
                },
                {
                    name: '🐛 Reportar Bug',
                    value: 'Reporte problemas e bugs encontrados',
                    inline: true
                },
                {
                    name: '💡 Sugestões',
                    value: 'Envie ideias e sugestões de melhorias',
                    inline: true
                }
            )
            .setFooter({ text: 'Estamos aqui para ajudar você!' })
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('acessar_documentacao')
                    .setLabel('Documentação')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('📚'),
                new ButtonBuilder()
                    .setCustomId('ver_tutoriais')
                    .setLabel('Tutoriais')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🎥'),
                new ButtonBuilder()
                    .setCustomId('contatar_suporte')
                    .setLabel('Suporte Técnico')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('💬')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('ver_faq')
                    .setLabel('FAQ')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('❓'),
                new ButtonBuilder()
                    .setCustomId('reportar_bug')
                    .setLabel('Reportar Bug')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🐛'),
                new ButtonBuilder()
                    .setCustomId('enviar_sugestao')
                    .setLabel('Enviar Sugestão')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('💡')
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('back_to_config')
                    .setLabel('Voltar ao Menu')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row1, row2, row3] });
    }

    // ===== HANDLERS DAS SUBCATEGORIAS =====

    async handleHabilitarPix(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('🏦 Habilitar PIX')
            .setDescription('Configure o PIX como método de pagamento na sua loja:')
            .setColor(0x00ff88)
            .addFields(
                {
                    name: '✅ Vantagens do PIX:',
                    value: '• Pagamentos instantâneos 24/7\n• Sem taxas para o cliente\n• Confirmação automática\n• Maior conversão de vendas\n• Disponível para todos os bancos',
                    inline: false
                },
                {
                    name: '⚙️ Configuração:',
                    value: 'Para habilitar o PIX, você precisa:\n• Ter uma chave PIX cadastrada\n• Configurar as credenciais de recebimento\n• Ativar o método na sua loja',
                    inline: false
                },
                {
                    name: '📊 Status Atual:',
                    value: '❌ PIX não configurado\n⏳ Aguardando configuração',
                    inline: false
                }
            )
            .setFooter({ text: 'Configure sua chave PIX para começar a receber pagamentos' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('configurar_chave_pix')
                    .setLabel('Configurar Chave PIX')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('🔑'),
                new ButtonBuilder()
                    .setCustomId('testar_pix')
                    .setLabel('Testar PIX')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🧪'),
                new ButtonBuilder()
                    .setCustomId('back_to_config_pagamentos')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }

    async handleAutenticarPix(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('🔑 Autenticar com PIX')
            .setDescription('Configure suas credenciais PIX para receber pagamentos:')
            .setColor(0x00d4aa)
            .addFields(
                {
                    name: '🔐 Dados Necessários:',
                    value: '• **Chave PIX** (CPF, CNPJ, Email, Telefone ou Aleatória)\n• **Nome do titular da conta**\n• **Banco da conta PIX**\n• **Tipo da chave PIX**',
                    inline: false
                },
                {
                    name: '📋 Tipos de Chave PIX:',
                    value: '• **CPF:** 123.456.789-00\n• **CNPJ:** 12.345.678/0001-90\n• **Email:** <EMAIL>\n• **Telefone:** +5511999999999\n• **Aleatória:** 123e4567-e12b-12d3-a456-426614174000',
                    inline: false
                },
                {
                    name: '🔒 Segurança:',
                    value: 'Suas informações são criptografadas e armazenadas com segurança.',
                    inline: false
                }
            )
            .setFooter({ text: 'Clique em "Inserir Chave PIX" para configurar' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('inserir_chave_pix')
                    .setLabel('Inserir Chave PIX')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🔑'),
                new ButtonBuilder()
                    .setCustomId('validar_chave_pix')
                    .setLabel('Validar Chave')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('✅'),
                new ButtonBuilder()
                    .setCustomId('back_to_config_pagamentos')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }

    async handleConfigMercadoPago(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('🛒 Configurar Mercado Pago')
            .setDescription('Configure sua integração com o Mercado Pago:')
            .setColor(0x009ee3)
            .addFields(
                {
                    name: '💳 Métodos Disponíveis:',
                    value: '• **Cartão de Crédito** (Visa, Master, Elo, etc.)\n• **PIX** via Mercado Pago\n• **Boleto Bancário**\n• **Parcelamento** automático',
                    inline: false
                },
                {
                    name: '⚙️ Configuração Atual:',
                    value: '❌ Mercado Pago não configurado\n⏳ Aguardando credenciais',
                    inline: false
                },
                {
                    name: '🔑 Credenciais Necessárias:',
                    value: '• **Access Token** (APP_USR-...)\n• **Public Key** (APP_USR-...)\n• **Client Secret** (opcional)',
                    inline: false
                }
            )
            .setFooter({ text: 'Configure suas credenciais para ativar o Mercado Pago' })
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('configurar_credenciais_mp')
                    .setLabel('Configurar Credenciais')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🔑'),
                new ButtonBuilder()
                    .setCustomId('testar_mercado_pago')
                    .setLabel('Testar Integração')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('🧪'),
                new ButtonBuilder()
                    .setLabel('Painel Mercado Pago')
                    .setStyle(ButtonStyle.Link)
                    .setURL('https://www.mercadopago.com.br/developers/panel')
                    .setEmoji('🔗')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('ativar_checkout_pro')
                    .setLabel('Ativar Checkout Pro')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('💳'),
                new ButtonBuilder()
                    .setCustomId('ativar_checkout_transparente')
                    .setLabel('Ativar Checkout Transparente')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⚡'),
                new ButtonBuilder()
                    .setCustomId('back_to_config_pagamentos')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });
    }

    async handleGerenciarProdutos(interaction) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};

        // Calcular estatísticas reais
        const totalProdutos = userData.products ? userData.products.length : 0;
        const totalCategorias = userData.categories ? userData.categories.length : 0;
        const produtosAtivos = userData.products ? userData.products.filter(p => p.details.stock > 0).length : 0;
        const produtosInativos = totalProdutos - produtosAtivos;

        const embed = new EmbedBuilder()
            .setTitle('📦 Gerenciar Produtos')
            .setDescription('Gerencie todos os produtos da sua loja:')
            .setColor(0x2196f3)
            .addFields(
                {
                    name: '📊 Estatísticas:',
                    value: `• **Total de produtos:** ${totalProdutos}\n• **Produtos ativos:** ${produtosAtivos}\n• **Produtos inativos:** ${produtosInativos}\n• **Categorias:** ${totalCategorias}`,
                    inline: false
                },
                {
                    name: '⚡ Ações Rápidas:',
                    value: 'Adicione, edite ou remova produtos da sua loja de forma simples e rápida.',
                    inline: false
                }
            )
            .setFooter({ text: 'Selecione uma ação para gerenciar seus produtos' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('criar_produto')
                    .setLabel('Criar Produto')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('➕'),
                new ButtonBuilder()
                    .setCustomId('editar_produto')
                    .setLabel('Editar Produto')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('✏️'),
                new ButtonBuilder()
                    .setCustomId('remover_produto')
                    .setLabel('Remover Produto')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🗑️'),
                new ButtonBuilder()
                    .setCustomId('back_to_config_loja')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }

    // ===== HANDLERS DAS CATEGORIAS DE AUTENTICAÇÃO =====

    async handleAuthMetodosPagamento(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('💳 Métodos de Pagamento')
            .setDescription('Configure todos os métodos de pagamento disponíveis:')
            .setColor(0x00d4aa)
            .addFields(
                {
                    name: '🛒 Mercado Pago',
                    value: 'Cartão, PIX, boleto e parcelamento',
                    inline: true
                },
                {
                    name: '🏦 PIX Direto',
                    value: 'Recebimento direto via chave PIX',
                    inline: true
                },
                {
                    name: '💳 Cartões de Crédito',
                    value: 'Visa, Master, Elo e outros',
                    inline: true
                },
                {
                    name: '🏛️ Efi Bank',
                    value: 'Gateway Efi Bank (em breve)',
                    inline: true
                },
                {
                    name: '💰 PagSeguro',
                    value: 'Integração PagSeguro (em breve)',
                    inline: true
                },
                {
                    name: '🌐 PayPal',
                    value: 'Pagamentos internacionais (em breve)',
                    inline: true
                }
            )
            .setFooter({ text: 'Selecione um método para configurar' })
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('config_mercado_pago_auth')
                    .setLabel('Mercado Pago')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🛒'),
                new ButtonBuilder()
                    .setCustomId('config_pix_direto')
                    .setLabel('PIX Direto')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🏦'),
                new ButtonBuilder()
                    .setCustomId('config_cartoes')
                    .setLabel('Cartões de Crédito')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('💳')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('config_efi_bank')
                    .setLabel('Efi Bank')
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(true)
                    .setEmoji('🏛️'),
                new ButtonBuilder()
                    .setCustomId('config_pagseguro')
                    .setLabel('PagSeguro')
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(true)
                    .setEmoji('💰'),
                new ButtonBuilder()
                    .setCustomId('back_to_auth_main')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });
    }

    async handleAuthGateways(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('🏦 Gateways de Pagamento')
            .setDescription('Configure e gerencie seus gateways de pagamento:')
            .setColor(0x2196f3)
            .addFields(
                {
                    name: '🔗 Integrações Ativas',
                    value: 'Visualize e gerencie suas integrações ativas',
                    inline: true
                },
                {
                    name: '⚙️ Configurar Novo Gateway',
                    value: 'Adicione novos provedores de pagamento',
                    inline: true
                },
                {
                    name: '🧪 Testar Conexões',
                    value: 'Valide suas integrações e conexões',
                    inline: true
                },
                {
                    name: '📊 Status dos Gateways',
                    value: 'Monitore o status de cada gateway',
                    inline: true
                },
                {
                    name: '🔄 Sincronização',
                    value: 'Sincronize dados entre gateways',
                    inline: true
                },
                {
                    name: '⚡ Failover',
                    value: 'Configure backup automático',
                    inline: true
                }
            )
            .setFooter({ text: 'Gerencie seus gateways de pagamento' })
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('ver_integracoes_ativas')
                    .setLabel('Integrações Ativas')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🔗'),
                new ButtonBuilder()
                    .setCustomId('adicionar_gateway')
                    .setLabel('Adicionar Gateway')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('➕'),
                new ButtonBuilder()
                    .setCustomId('testar_gateways')
                    .setLabel('Testar Conexões')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🧪')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('status_gateways')
                    .setLabel('Status dos Gateways')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📊'),
                new ButtonBuilder()
                    .setCustomId('configurar_failover')
                    .setLabel('Configurar Failover')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⚡'),
                new ButtonBuilder()
                    .setCustomId('back_to_auth_main')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });
    }

    async handleAuthSeguranca(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('🔐 Segurança e Credenciais')
            .setDescription('Gerencie tokens, chaves de API e configurações de segurança:')
            .setColor(0xf44336)
            .addFields(
                {
                    name: '🔑 Tokens de API',
                    value: 'Gerencie tokens de acesso e autenticação',
                    inline: true
                },
                {
                    name: '🗝️ Chaves de Criptografia',
                    value: 'Configure chaves de segurança',
                    inline: true
                },
                {
                    name: '🛡️ Certificados SSL',
                    value: 'Gerencie certificados de segurança',
                    inline: true
                },
                {
                    name: '🔒 Controle de Acesso',
                    value: 'Configure permissões e acessos',
                    inline: true
                },
                {
                    name: '📋 Logs de Segurança',
                    value: 'Monitore tentativas de acesso',
                    inline: true
                },
                {
                    name: '⚠️ Alertas de Segurança',
                    value: 'Configure notificações de segurança',
                    inline: true
                }
            )
            .setFooter({ text: 'Mantenha sua loja segura' })
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('gerenciar_tokens')
                    .setLabel('Gerenciar Tokens')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🔑'),
                new ButtonBuilder()
                    .setCustomId('configurar_chaves')
                    .setLabel('Chaves de Criptografia')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🗝️'),
                new ButtonBuilder()
                    .setCustomId('certificados_ssl')
                    .setLabel('Certificados SSL')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🛡️')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('controle_acesso')
                    .setLabel('Controle de Acesso')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔒'),
                new ButtonBuilder()
                    .setCustomId('logs_seguranca')
                    .setLabel('Logs de Segurança')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📋'),
                new ButtonBuilder()
                    .setCustomId('back_to_auth_main')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });
    }

    async handleAuthPagamentosInstantaneos(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('⚡ Pagamentos Instantâneos')
            .setDescription('Configure métodos de pagamento em tempo real:')
            .setColor(0x4caf50)
            .addFields(
                {
                    name: '🏦 PIX',
                    value: 'Pagamentos instantâneos 24/7 via PIX',
                    inline: true
                },
                {
                    name: '💳 Cartão Instantâneo',
                    value: 'Aprovação imediata de cartões',
                    inline: true
                },
                {
                    name: '🔄 Transferências',
                    value: 'TED/DOC e transferências bancárias',
                    inline: true
                },
                {
                    name: '📱 Carteiras Digitais',
                    value: 'PicPay, PayPal, Apple Pay, Google Pay',
                    inline: true
                },
                {
                    name: '🌐 Criptomoedas',
                    value: 'Bitcoin, Ethereum e outras (em breve)',
                    inline: true
                },
                {
                    name: '⚡ Configurações',
                    value: 'Limites, taxas e configurações gerais',
                    inline: true
                }
            )
            .setFooter({ text: 'Configure pagamentos instantâneos' })
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('config_pix_instantaneo')
                    .setLabel('Configurar PIX')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🏦'),
                new ButtonBuilder()
                    .setCustomId('config_cartao_instantaneo')
                    .setLabel('Cartão Instantâneo')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('💳'),
                new ButtonBuilder()
                    .setCustomId('config_transferencias')
                    .setLabel('Transferências')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🔄')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('config_carteiras_digitais')
                    .setLabel('Carteiras Digitais')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📱'),
                new ButtonBuilder()
                    .setCustomId('config_limites_instantaneo')
                    .setLabel('Limites e Taxas')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⚙️'),
                new ButtonBuilder()
                    .setCustomId('back_to_auth_main')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });
    }

    async handleAuthTestes(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('🧪 Testes e Validação')
            .setDescription('Teste e valide todas as suas configurações de pagamento:')
            .setColor(0xff9800)
            .addFields(
                {
                    name: '🔍 Testar Integrações',
                    value: 'Valide conexões com gateways',
                    inline: true
                },
                {
                    name: '💳 Simular Pagamentos',
                    value: 'Teste fluxos de pagamento completos',
                    inline: true
                },
                {
                    name: '🏦 Validar PIX',
                    value: 'Teste chaves PIX e recebimentos',
                    inline: true
                },
                {
                    name: '📊 Relatórios de Teste',
                    value: 'Visualize resultados dos testes',
                    inline: true
                },
                {
                    name: '🐛 Debug e Logs',
                    value: 'Analise logs de erro e debug',
                    inline: true
                },
                {
                    name: '✅ Validação Final',
                    value: 'Confirme que tudo está funcionando',
                    inline: true
                }
            )
            .setFooter({ text: 'Teste antes de colocar em produção' })
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('testar_integracoes')
                    .setLabel('Testar Integrações')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🔍'),
                new ButtonBuilder()
                    .setCustomId('simular_pagamentos')
                    .setLabel('Simular Pagamentos')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('💳'),
                new ButtonBuilder()
                    .setCustomId('validar_pix_teste')
                    .setLabel('Validar PIX')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🏦')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('relatorios_teste')
                    .setLabel('Relatórios de Teste')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📊'),
                new ButtonBuilder()
                    .setCustomId('debug_logs')
                    .setLabel('Debug e Logs')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🐛'),
                new ButtonBuilder()
                    .setCustomId('back_to_auth_main')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });
    }

    async handleAuthMonitoramento(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('📊 Monitoramento')
            .setDescription('Monitore transações, logs e performance em tempo real:')
            .setColor(0x673ab7)
            .addFields(
                {
                    name: '💰 Transações em Tempo Real',
                    value: 'Acompanhe pagamentos ao vivo',
                    inline: true
                },
                {
                    name: '📈 Métricas de Performance',
                    value: 'Taxa de sucesso, latência, erros',
                    inline: true
                },
                {
                    name: '🚨 Alertas e Notificações',
                    value: 'Receba alertas de problemas',
                    inline: true
                },
                {
                    name: '📋 Logs Detalhados',
                    value: 'Histórico completo de operações',
                    inline: true
                },
                {
                    name: '🔍 Análise de Erros',
                    value: 'Identifique e resolva problemas',
                    inline: true
                },
                {
                    name: '📊 Dashboards',
                    value: 'Visualizações e relatórios',
                    inline: true
                }
            )
            .setFooter({ text: 'Mantenha tudo sob controle' })
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('transacoes_tempo_real')
                    .setLabel('Transações ao Vivo')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('💰'),
                new ButtonBuilder()
                    .setCustomId('metricas_performance')
                    .setLabel('Métricas de Performance')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('📈'),
                new ButtonBuilder()
                    .setCustomId('configurar_alertas')
                    .setLabel('Configurar Alertas')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🚨')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('ver_logs_detalhados')
                    .setLabel('Logs Detalhados')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📋'),
                new ButtonBuilder()
                    .setCustomId('dashboard_monitoramento')
                    .setLabel('Dashboard')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📊'),
                new ButtonBuilder()
                    .setCustomId('back_to_auth_main')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });
    }

    async handleConfigProdutos(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('📦 Configuração de Produtos')
            .setDescription('Configure os produtos que serão vendidos pelo seu bot:')
            .setColor(0x0099ff)
            .addFields(
                {
                    name: '🛍️ Gerenciar Produtos:',
                    value: 'Adicione, edite ou remova produtos do seu catálogo',
                    inline: false
                },
                {
                    name: '📋 Informações do Produto:',
                    value: '• Nome do produto\n• Descrição detalhada\n• Preço\n• Categoria\n• Estoque disponível',
                    inline: false
                }
            )
            .setFooter({ text: 'Selecione uma ação abaixo' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('add_produto')
                    .setLabel('Adicionar Produto')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('➕'),
                new ButtonBuilder()
                    .setCustomId('edit_produto')
                    .setLabel('Editar Produto')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('✏️'),
                new ButtonBuilder()
                    .setCustomId('remove_produto')
                    .setLabel('Remover Produto')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🗑️'),
                new ButtonBuilder()
                    .setCustomId('list_produtos')
                    .setLabel('Listar Produtos')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📋'),
                new ButtonBuilder()
                    .setCustomId('back_to_config')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }

    async handleConfigPrecos(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('💰 Configuração de Preços')
            .setDescription('Configure os preços e promoções dos seus produtos:')
            .setColor(0xffd700)
            .addFields(
                {
                    name: '💵 Gerenciar Preços:',
                    value: 'Defina preços individuais ou em lote para seus produtos',
                    inline: false
                },
                {
                    name: '🎯 Promoções:',
                    value: '• Descontos por quantidade\n• Promoções temporárias\n• Cupons de desconto\n• Preços especiais',
                    inline: false
                }
            )
            .setFooter({ text: 'Selecione uma ação abaixo' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('set_precos')
                    .setLabel('Definir Preços')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('💰'),
                new ButtonBuilder()
                    .setCustomId('create_promocao')
                    .setLabel('Criar Promoção')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('🎯'),
                new ButtonBuilder()
                    .setCustomId('manage_cupons')
                    .setLabel('Gerenciar Cupons')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🎫'),
                new ButtonBuilder()
                    .setCustomId('back_to_config')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }

    async handleConfigMensagens(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('💬 Configuração de Mensagens')
            .setDescription('Personalize as mensagens do seu bot de vendas:')
            .setColor(0x9b59b6)
            .addFields(
                {
                    name: '📝 Tipos de Mensagens:',
                    value: 'Configure mensagens personalizadas para diferentes situações',
                    inline: false
                },
                {
                    name: '🎨 Personalização:',
                    value: '• Mensagem de boas-vindas\n• Confirmação de compra\n• Entrega de produto\n• Suporte ao cliente\n• Mensagens de erro',
                    inline: false
                }
            )
            .setFooter({ text: 'Selecione uma ação abaixo' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('edit_welcome_msg')
                    .setLabel('Mensagem de Boas-vindas')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('👋'),
                new ButtonBuilder()
                    .setCustomId('edit_purchase_msg')
                    .setLabel('Confirmação de Compra')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('✅'),
                new ButtonBuilder()
                    .setCustomId('edit_delivery_msg')
                    .setLabel('Entrega de Produto')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('📦'),
                new ButtonBuilder()
                    .setCustomId('edit_support_msg')
                    .setLabel('Mensagem de Suporte')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🆘'),
                new ButtonBuilder()
                    .setCustomId('back_to_config')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }

    async handleConfigCanais(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('📺 Configuração de Canais')
            .setDescription('Configure os canais onde o bot irá operar:')
            .setColor(0x7289da)
            .addFields(
                {
                    name: '🎯 Canais de Vendas:',
                    value: 'Defina onde o bot pode processar vendas e interagir com clientes',
                    inline: false
                },
                {
                    name: '⚙️ Configurações:',
                    value: '• Canal de vendas principal\n• Canais de suporte\n• Canal de logs\n• Canal de notificações\n• Canais permitidos para comandos',
                    inline: false
                }
            )
            .setFooter({ text: 'Selecione uma ação abaixo' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('set_sales_channel')
                    .setLabel('Canal de Vendas')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🛒'),
                new ButtonBuilder()
                    .setCustomId('set_support_channel')
                    .setLabel('Canal de Suporte')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🆘'),
                new ButtonBuilder()
                    .setCustomId('set_logs_channel')
                    .setLabel('Canal de Logs')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('📋'),
                new ButtonBuilder()
                    .setCustomId('set_notifications_channel')
                    .setLabel('Canal de Notificações')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🔔'),
                new ButtonBuilder()
                    .setCustomId('back_to_config')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }

    async handleConfigRoles(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('🎭 Configuração de Cargos')
            .setDescription('Configure os cargos e permissões do seu bot:')
            .setColor(0xe91e63)
            .addFields(
                {
                    name: '👑 Gerenciar Cargos:',
                    value: 'Defina quais cargos podem usar o bot e suas permissões',
                    inline: false
                },
                {
                    name: '🔐 Permissões:',
                    value: '• Cargo de administrador\n• Cargo de vendedor\n• Cargo de cliente VIP\n• Cargos automáticos após compra\n• Permissões especiais',
                    inline: false
                }
            )
            .setFooter({ text: 'Selecione uma ação abaixo' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('set_admin_role')
                    .setLabel('Cargo Admin')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('👑'),
                new ButtonBuilder()
                    .setCustomId('set_seller_role')
                    .setLabel('Cargo Vendedor')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('💼'),
                new ButtonBuilder()
                    .setCustomId('set_vip_role')
                    .setLabel('Cargo VIP')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⭐'),
                new ButtonBuilder()
                    .setCustomId('set_auto_roles')
                    .setLabel('Cargos Automáticos')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('🤖'),
                new ButtonBuilder()
                    .setCustomId('back_to_config')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }

    async handleModificarDadosMP(interaction) {
        const userId = interaction.user.id;
        const credentials = this.getUserCredentials(userId);
        
        const embed = new EmbedBuilder()
            .setTitle('📝 Modificar MP')
            .setDescription('Aqui você pode atualizar suas credenciais do Mercado Pago.')
            .setColor(0x5865F2)
            .addFields(
                {
                    name: '🔑 Credenciais Atuais',
                    value: `**Access Token:** ${credentials.accessToken ? '✅ Configurado' : '❌ Não configurado'}\n**Client Secret:** ${credentials.clientSecret ? '✅ Configurado' : '❌ Não configurado'}`,
                    inline: false
                },
                {
                    name: '👤 Conta Vinculada',
                    value: credentials.accountInfo ? `**Email:** ${credentials.accountInfo.email}\n**Nome:** ${credentials.accountInfo.nickname}` : 'Nenhuma conta vinculada',
                    inline: false
                },
                {
                    name: '📋 Instruções',
                    value: 'Selecione uma das opções abaixo para atualizar suas credenciais.',
                    inline: false
                }
            )
            .setFooter({ text: 'Suas credenciais são armazenadas de forma segura' })
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_access_token')
                    .setLabel('Atualizar Access Token')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔑'),
                new ButtonBuilder()
                    .setCustomId('setup_client_secret')
                    .setLabel('Atualizar Client Secret')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔐')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('test_mercado_pago_connection')
                    .setLabel('Testar Conexão')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🧪'),
                new ButtonBuilder()
                    .setCustomId('clear_mercado_pago_credentials')
                    .setLabel('Excluir Credenciais')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🗑️')
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('back_to_config_pagamentos')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row1, row2, row3] });
    }

    async handleToggleSitePagamento(interaction) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        
        // Inverter o estado atual (ou definir como false se não existir)
        userData.sitePagamentoAtivo = userData.sitePagamentoAtivo === undefined ? false : !userData.sitePagamentoAtivo;
        database.saveUser(userId, userData);
        
        // Usar o método auxiliar para gerar embed e componentes
        const { embed, components } = this.buildPagamentosContent(userId);
        
        // Atualizar a interface
        await interaction.update({ embeds: [embed], components });
    }

    async handleToggleSistema(interaction) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};
        
        // Inverter o estado atual (ou definir como false se não existir)
        userData.sistemaAtivo = userData.sistemaAtivo === undefined ? false : !userData.sistemaAtivo;
        database.saveUser(userId, userData);
        
        // Usar o método auxiliar para gerar embed e componentes
        const { embed, components } = this.buildPagamentosContent(userId);
        
        // Atualizar a interface
        await interaction.update({ embeds: [embed], components });
    }

    // Atualizar o método para Ver Taxas do Plano
    async handleConfigTaxasPagamento(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('💰 Taxas do Plano Atual')
            .setDescription('Confira as taxas aplicadas ao seu plano atual:')
            .setColor(0xf1c40f)
            .addFields(
                {
                    name: '📊 Plano Atual',
                    value: '**Plano Standard**',
                    inline: false
                },
                {
                    name: '💳 Cartão de Crédito',
                    value: '• Taxa fixa: 2.99%\n• Taxa por transação: R$ 0,40\n• Parcelamento: +2% por parcela',
                    inline: true
                },
                {
                    name: '🏦 PIX',
                    value: '• Taxa fixa: 0.99%\n• Taxa por transação: R$ 0,10\n• Sem taxas adicionais',
                    inline: true
                },
                {
                    name: '📄 Boleto',
                    value: '• Taxa fixa: 1.99%\n• Taxa por transação: R$ 3,00\n• Prazo: 3 dias úteis',
                    inline: true
                },
                {
                    name: '💸 Saques',
                    value: '• Saque mínimo: R$ 50,00\n• Prazo: 1 dia útil (D+1)\n• Taxa de saque: Gratuito',
                    inline: false
                },
                {
                    name: '🔄 Planos Disponíveis',
                    value: '• **Standard**: Plano atual\n• **Premium**: Taxas reduzidas\n• **Enterprise**: Taxas personalizadas',
                    inline: false
                }
            )
            .setFooter({ text: 'Para alterar seu plano, entre em contato com o suporte' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('solicitar_upgrade')
                    .setLabel('Solicitar Upgrade')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⬆️'),
                new ButtonBuilder()
                    .setCustomId('back_to_config_pagamentos')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }

    // Método auxiliar para construir o conteúdo da página de configuração de pagamentos
    buildPagamentosContent(userId) {
        const userData = database.getUser(userId) || {};
        
        // Verificar estados do sistema
        const sistemaAtivo = userData.sistemaAtivo !== false; // Por padrão é ativo se não estiver definido
        const sitePagamentoAtivo = userData.sitePagamentoAtivo !== false; // Por padrão é ativo se não estiver definido
        const mpConfigurado = userData.mercadoPagoConfirmed === true;
        
        // Definir cores com base no status
        const statusColor = sistemaAtivo ? 0x00d4aa : 0xff3860;
        
        const embed = new EmbedBuilder()
            .setTitle('💳 Central de Pagamentos')
            .setDescription(`**Status do Sistema:** ${sistemaAtivo ? '🟢 **ATIVO**' : '🔴 **INATIVO**'}`)
            .setColor(statusColor)
            .addFields(
                {
                    name: '🛒 Mercado Pago',
                    value: `Status: ${mpConfigurado ? '✅ **Configurado**' : '⚠️ **Não configurado**'}\nAceite cartões, PIX e boleto com processamento automático.`,
                    inline: false
                },
                {
                    name: '🌐 Pagamento via Site',
                    value: `Status: ${sitePagamentoAtivo ? '✅ **Ativo**' : '❌ **Desativado**'}\nOs clientes ${sitePagamentoAtivo ? 'serão' : 'não serão'} redirecionados para o site do Mercado Pago.`,
                    inline: false
                },
                {
                    name: '⚙️ Gerenciamento do Sistema',
                    value: `Configure e monitore todos os aspectos do seu sistema de pagamentos.\nÚltima atualização: ${new Date().toLocaleString()}`,
                    inline: false
                }
            )
            .setFooter({ text: 'Sistema de Pagamentos Profissional • Nodex Solutions' })
            .setTimestamp();

        // Definir estilos e labels dinâmicos com base no status
        const siteButtonStyle = sitePagamentoAtivo ? ButtonStyle.Success : ButtonStyle.Secondary;
        const sistemaButtonStyle = sistemaAtivo ? ButtonStyle.Success : ButtonStyle.Danger;
        
        const siteButtonLabel = sitePagamentoAtivo ? 'Pagar pelo Site ✅' : 'Pagar pelo Site ❌';
        const sistemaButtonLabel = sistemaAtivo ? 'Sistema Ativo' : 'Sistema Inativo';
        
        // Reorganização dos botões em duas linhas para melhor hierarquia visual
        // Ordem: ativar e desativar sistema, Modificar MP, Pagar pelo site, Ver taxas, Voltar
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('toggle_sistema')
                    .setLabel(sistemaButtonLabel)
                    .setStyle(sistemaButtonStyle)
                    .setEmoji(sistemaAtivo ? '🟢' : '🔴'),
                new ButtonBuilder()
                    .setCustomId('modificar_dados_mp')
                    .setLabel('Modificar MP')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📝'),
                new ButtonBuilder()
                    .setCustomId('toggle_site_pagamento')
                    .setLabel(siteButtonLabel)
                    .setStyle(siteButtonStyle)
                    .setEmoji('🌐')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('config_taxas_pagamento')
                    .setLabel('Ver Taxas')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('💰'),
                new ButtonBuilder()
                    .setCustomId('gerenciar_bancos_bloqueados')
                    .setLabel('Bancos Bloqueados')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🚫'),
                new ButtonBuilder()
                    .setCustomId('gerenciar_cupons')
                    .setLabel('Cupons')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🎫'),
                new ButtonBuilder()
                    .setCustomId('back_to_config')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );
            
        // Removemos a terceira linha para uma interface mais limpa
            
        return { embed, components: [row1, row2] };
    }

    async handleGerenciarBancosBloqueados(interaction) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};

        // Lista de bancos disponíveis para bloqueio
        const bancosDisponiveis = [
            { id: 'inter', name: 'Banco Inter', emoji: '🟠' },
            { id: 'nubank', name: 'Nubank', emoji: '🟣' },
            { id: 'picpay', name: 'PicPay', emoji: '🟢' },
            { id: 'mercadopago', name: 'Mercado Pago', emoji: '💙' },
            { id: 'c6bank', name: 'C6 Bank', emoji: '⚫' },
            { id: 'neon', name: 'Neon', emoji: '🔵' },
            { id: 'next', name: 'Next', emoji: '🟤' },
            { id: 'original', name: 'Banco Original', emoji: '🟡' },
            { id: 'digio', name: 'Digio', emoji: '🔴' },
            { id: 'will', name: 'Will Bank', emoji: '🟢' }
        ];

        // Obter bancos bloqueados do usuário
        const bancosBloqueados = userData.bancosBloqueados || [];

        // Criar embed
        const embed = new EmbedBuilder()
            .setTitle('🚫 Gerenciar Bancos Bloqueados')
            .setDescription('**Configure quais bancos devem ser bloqueados para pagamentos PIX**\n\nBloqueie bancos que você considera de risco para evitar golpes e chargebacks.')
            .setColor(0xff3860)
            .addFields(
                {
                    name: '🛡️ Proteção Contra Golpes',
                    value: 'Bloqueie bancos digitais que facilitam a criação de contas falsas ou que têm histórico de problemas com chargebacks.',
                    inline: false
                },
                {
                    name: '📊 Status Atual',
                    value: bancosBloqueados.length > 0 ?
                        `🚫 **${bancosBloqueados.length} banco(s) bloqueado(s)**` :
                        '✅ **Nenhum banco bloqueado**',
                    inline: false
                }
            )
            .setFooter({ text: 'Clique nos bancos para bloquear/desbloquear' })
            .setTimestamp();

        // Adicionar lista de bancos bloqueados se houver
        if (bancosBloqueados.length > 0) {
            const bancosBloqueadosNomes = bancosBloqueados.map(bancoId => {
                const banco = bancosDisponiveis.find(b => b.id === bancoId);
                return banco ? `${banco.emoji} ${banco.name}` : bancoId;
            }).join('\n');

            embed.addFields({
                name: '🚫 Bancos Bloqueados',
                value: bancosBloqueadosNomes,
                inline: false
            });
        }

        // Criar botões para cada banco
        const rows = [];
        for (let i = 0; i < bancosDisponiveis.length; i += 3) {
            const row = new ActionRowBuilder();
            const bancosNaLinha = bancosDisponiveis.slice(i, i + 3);

            bancosNaLinha.forEach(banco => {
                const isBloqueado = bancosBloqueados.includes(banco.id);
                const button = new ButtonBuilder()
                    .setCustomId(`toggle_banco_${banco.id}`)
                    .setLabel(banco.name)
                    .setStyle(isBloqueado ? ButtonStyle.Danger : ButtonStyle.Secondary)
                    .setEmoji(isBloqueado ? '🚫' : banco.emoji);

                row.addComponents(button);
            });

            rows.push(row);
        }

        // Adicionar botões de ação
        const actionRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('bloquear_todos_bancos')
                    .setLabel('Bloquear Todos')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🚫'),
                new ButtonBuilder()
                    .setCustomId('desbloquear_todos_bancos')
                    .setLabel('Desbloquear Todos')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('✅'),
                new ButtonBuilder()
                    .setCustomId('back_to_config_pagamentos')
                    .setLabel('Voltar')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );

        rows.push(actionRow);

        await interaction.update({ embeds: [embed], components: rows });
    }

    async handleToggleBanco(interaction, bancoId) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};

        // Obter bancos bloqueados atuais
        let bancosBloqueados = userData.bancosBloqueados || [];

        // Toggle do banco
        if (bancosBloqueados.includes(bancoId)) {
            // Remover da lista (desbloquear)
            bancosBloqueados = bancosBloqueados.filter(id => id !== bancoId);
        } else {
            // Adicionar à lista (bloquear)
            bancosBloqueados.push(bancoId);
        }

        // Salvar no banco de dados
        userData.bancosBloqueados = bancosBloqueados;
        database.saveUser(userId, userData);

        // Atualizar interface
        await this.handleGerenciarBancosBloqueados(interaction);
    }

    async handleBloquearTodosBancos(interaction) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};

        // Lista de todos os bancos disponíveis
        const todosBancos = ['inter', 'nubank', 'picpay', 'mercadopago', 'c6bank', 'neon', 'next', 'original', 'digio', 'will'];

        // Bloquear todos
        userData.bancosBloqueados = [...todosBancos];
        database.saveUser(userId, userData);

        // Atualizar interface
        await this.handleGerenciarBancosBloqueados(interaction);
    }

    async handleDesbloquearTodosBancos(interaction) {
        const userId = interaction.user.id;
        const userData = database.getUser(userId) || {};

        // Desbloquear todos
        userData.bancosBloqueados = [];
        database.saveUser(userId, userData);

        // Atualizar interface
        await this.handleGerenciarBancosBloqueados(interaction);
    }

    async handleGerenciarCupons(interaction) {
        try {
            const userId = interaction.user.id;
            const userData = database.getUser(userId) || {};

            // Inicializar cupons se não existir
            if (!userData.cupons) {
                userData.cupons = [];
            }

            const cuponsAtivos = userData.cupons.filter(cupom => cupom.ativo);
            const cuponsInativos = userData.cupons.filter(cupom => !cupom.ativo);

            const embed = new EmbedBuilder()
                .setTitle('🎫 Gerenciar Cupons de Desconto')
                .setDescription('Crie e gerencie cupons de desconto para seus produtos.')
                .setColor(0x9c27b0)
                .addFields(
                    { name: '📊 Estatísticas:', value: `✅ **Ativos:** ${cuponsAtivos.length}\n❌ **Inativos:** ${cuponsInativos.length}\n📈 **Total:** ${userData.cupons.length}`, inline: true },
                    { name: '💡 Tipos de Desconto:', value: '🔢 **Percentual:** 10%, 25%, 50%\n💰 **Valor Fixo:** R$ 5,00, R$ 10,00\n🎯 **Produto Específico:** Apenas um produto', inline: true },
                    { name: '⚙️ Configurações:', value: '📅 **Validade:** Data de expiração\n🔢 **Limite de Uso:** Quantas vezes pode ser usado\n👥 **Uso por Usuário:** Limite individual', inline: true }
                )
                .setTimestamp()
                .setFooter({ text: 'Sistema de Cupons de Desconto' });

            const buttons = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('criar_cupom')
                        .setLabel('Criar Cupom')
                        .setStyle(ButtonStyle.Success)
                        .setEmoji('➕'),
                    new ButtonBuilder()
                        .setCustomId('listar_cupons')
                        .setLabel('Listar Cupons')
                        .setStyle(ButtonStyle.Primary)
                        .setEmoji('📋'),
                    new ButtonBuilder()
                        .setCustomId('estatisticas_cupons')
                        .setLabel('Estatísticas')
                        .setStyle(ButtonStyle.Secondary)
                        .setEmoji('📊'),
                    new ButtonBuilder()
                        .setCustomId('voltar_pagamentos')
                        .setLabel('Voltar')
                        .setStyle(ButtonStyle.Secondary)
                        .setEmoji('⬅️')
                );

            await interaction.update({ embeds: [embed], components: [buttons] });

        } catch (error) {
            console.error('Erro ao gerenciar cupons:', error.message);
            await interaction.reply({ content: '❌ Erro ao carregar sistema de cupons.', ephemeral: true });
        }
    }

    async registerCommission(sellerId, productId, totalValue, commissionAmount, paymentData) {
        try {
            // Obter dados globais de comissões
            if (!global.commissions) {
                global.commissions = new Map();
            }

            // Criar registro de comissão
            const commissionId = `COMM_${Date.now()}_${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
            const commissionRecord = {
                id: commissionId,
                sellerId: sellerId,
                buyerId: paymentData.buyerId,
                productId: productId,
                productName: paymentData.product.name,
                totalValue: totalValue,
                commissionAmount: commissionAmount,
                commissionRate: 0.08, // 8%
                sellerAmount: totalValue - commissionAmount,
                date: new Date().toISOString(),
                status: 'pending', // pending, collected, failed
                paymentData: {
                    preferenceId: paymentData.preferenceId,
                    cartData: paymentData.cartData
                }
            };

            // Salvar no sistema global
            global.commissions.set(commissionId, commissionRecord);

            // Salvar também no histórico do vendedor
            const sellerData = database.getUser(sellerId) || {};
            if (!sellerData.commissionHistory) {
                sellerData.commissionHistory = [];
            }
            sellerData.commissionHistory.push(commissionRecord);

            // Atualizar totais do vendedor
            if (!sellerData.commissionTotals) {
                sellerData.commissionTotals = {
                    totalSales: 0,
                    totalCommissions: 0,
                    totalEarnings: 0,
                    salesCount: 0
                };
            }

            sellerData.commissionTotals.totalSales += totalValue;
            sellerData.commissionTotals.totalCommissions += commissionAmount;
            sellerData.commissionTotals.totalEarnings += (totalValue - commissionAmount);
            sellerData.commissionTotals.salesCount += 1;

            database.saveUser(sellerId, sellerData);

            console.log(`💰 Comissão registrada: ${commissionId} - R$ ${commissionAmount.toFixed(2)} de R$ ${totalValue.toFixed(2)}`);

            // Notificação de comissão removida - vendedor não precisa receber DM sobre isso
            // await this.notifySellerCommission(sellerId, commissionRecord);

        } catch (error) {
            console.error('Erro ao registrar comissão:', error.message);
        }
    }

    async notifySellerCommission(sellerId, commissionRecord) {
        try {
            const seller = await this.client.users.fetch(sellerId);

            const commissionEmbed = new EmbedBuilder()
                .setTitle('💰 Nova Comissão Registrada')
                .setDescription('Uma nova venda foi realizada e a comissão foi calculada.')
                .setColor(0xffd700)
                .addFields(
                    { name: '📦 Produto:', value: commissionRecord.productName, inline: true },
                    { name: '💰 Valor Total:', value: `R$ ${commissionRecord.totalValue.toFixed(2).replace('.', ',')}`, inline: true },
                    { name: '💸 Comissão (8%):', value: `R$ ${commissionRecord.commissionAmount.toFixed(2).replace('.', ',')}`, inline: true },
                    { name: '💵 Você Recebe:', value: `R$ ${commissionRecord.sellerAmount.toFixed(2).replace('.', ',')}`, inline: true },
                    { name: '🆔 ID da Comissão:', value: commissionRecord.id, inline: true },
                    { name: '📅 Data:', value: new Date(commissionRecord.date).toLocaleString('pt-BR'), inline: true },
                    { name: 'ℹ️ Informação:', value: 'A comissão de 8% é descontada automaticamente para manutenção do sistema.', inline: false }
                )
                .setTimestamp()
                .setFooter({ text: 'Sistema de Comissões' });

            await seller.send({ embeds: [commissionEmbed] });

        } catch (dmError) {
            console.log('Não foi possível enviar notificação de comissão para o vendedor:', dmError.message);
        }
    }

    async handleAprovarCommand(interaction) {
        try {
            const transacaoId = interaction.options.getString('transacao_id');
            const userId = interaction.user.id;

            console.log(`🔍 Tentativa de aprovação manual: ${transacaoId} por ${userId}`);

            // Buscar transação nos carrinhos ativos
            let cartData = null;
            let cartKey = null;

            if (global.carts) {
                for (const [key, data] of global.carts.entries()) {
                    if (data.transactionId === transacaoId) {
                        cartData = data;
                        cartKey = key;
                        break;
                    }
                }
            }

            if (!cartData) {
                await interaction.reply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Transação Não Encontrada')
                            .setDescription('A transação especificada não foi encontrada ou já foi processada.')
                            .setColor(0xff0000)
                            .addFields(
                                { name: '🆔 ID Informado:', value: `\`${transacaoId}\``, inline: true },
                                { name: '💡 Dica:', value: 'Verifique se o ID está correto e se a transação ainda está pendente.', inline: false }
                            )
                            .setTimestamp()
                            .setFooter({ text: 'Sistema de Aprovação Manual' })
                    ],
                    flags: 64 // MessageFlags.Ephemeral
                });
                return;
            }

            // Verificar se o usuário é o vendedor da transação
            if (cartData.sellerId !== userId) {
                await interaction.reply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('🚫 Acesso Negado')
                            .setDescription('Você só pode aprovar transações dos seus próprios produtos.')
                            .setColor(0xff0000)
                            .addFields(
                                { name: '👤 Vendedor da Transação:', value: `<@${cartData.sellerId}>`, inline: true },
                                { name: '🆔 ID da Transação:', value: `\`${transacaoId}\``, inline: true }
                            )
                            .setTimestamp()
                            .setFooter({ text: 'Sistema de Aprovação Manual' })
                    ],
                    flags: 64 // MessageFlags.Ephemeral
                });
                return;
            }

            // Buscar dados do produto
            const { product } = this.findProductAndSeller(cartData.productId);
            if (!product) {
                await interaction.reply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Produto Não Encontrado')
                            .setDescription('O produto relacionado a esta transação não foi encontrado.')
                            .setColor(0xff0000)
                            .addFields(
                                { name: '🆔 ID da Transação:', value: `\`${transacaoId}\``, inline: true },
                                { name: '📦 ID do Produto:', value: `\`${cartData.productId}\``, inline: true }
                            )
                            .setTimestamp()
                            .setFooter({ text: 'Sistema de Aprovação Manual' })
                    ],
                    flags: 64 // MessageFlags.Ephemeral
                });
                return;
            }

            // Confirmar aprovação com o vendedor
            const confirmEmbed = new EmbedBuilder()
                .setTitle('⚠️ Confirmar Aprovação Manual')
                .setDescription('Você está prestes a aprovar manualmente esta transação. Esta ação não pode ser desfeita.')
                .setColor(0xffa500)
                .addFields(
                    { name: '🆔 ID da Transação:', value: `\`${transacaoId}\``, inline: true },
                    { name: '👤 Comprador:', value: `<@${cartData.buyerId}>`, inline: true },
                    { name: '📦 Produto:', value: product.name, inline: true },
                    { name: '💰 Valor:', value: `R$ ${product.details.price.toFixed(2).replace('.', ',')}`, inline: true },
                    { name: '🛒 Quantidade:', value: cartData.quantity.toString(), inline: true },
                    { name: '💰 Total:', value: `R$ ${(product.details.price * cartData.quantity).toFixed(2).replace('.', ',')}`, inline: true },
                    { name: '📅 Criado em:', value: new Date(cartData.createdAt).toLocaleString('pt-BR'), inline: false },
                    { name: '⚠️ Importante:', value: 'Ao aprovar, o produto será entregue automaticamente e a comissão será calculada.', inline: false }
                )
                .setTimestamp()
                .setFooter({ text: 'Sistema de Aprovação Manual' });

            const confirmButtons = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`confirm_manual_approval_${transacaoId}`)
                        .setLabel('✅ Confirmar Aprovação')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId(`cancel_manual_approval_${transacaoId}`)
                        .setLabel('❌ Cancelar')
                        .setStyle(ButtonStyle.Danger)
                );

            await interaction.reply({
                embeds: [confirmEmbed],
                components: [confirmButtons],
                flags: 64 // MessageFlags.Ephemeral
            });

        } catch (error) {
            console.error('Erro ao processar comando /aprovar:', error.message);
            await interaction.reply({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('❌ Erro Interno')
                        .setDescription('Ocorreu um erro ao processar a aprovação manual.')
                        .setColor(0xff0000)
                        .addFields(
                            { name: '🔧 Erro:', value: error.message, inline: false },
                            { name: '💡 Solução:', value: 'Tente novamente em alguns segundos ou entre em contato com o suporte.', inline: false }
                        )
                        .setTimestamp()
                        .setFooter({ text: 'Sistema de Aprovação Manual' })
                ],
                flags: 64 // MessageFlags.Ephemeral
            });
        }
    }

    async handleConfirmManualApproval(interaction, transacaoId) {
        try {
            const userId = interaction.user.id;

            // Buscar transação nos carrinhos ativos
            let cartData = null;
            let cartKey = null;

            if (global.carts) {
                for (const [key, data] of global.carts.entries()) {
                    if (data.transactionId === transacaoId) {
                        cartData = data;
                        cartKey = key;
                        break;
                    }
                }
            }

            if (!cartData) {
                await interaction.update({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Transação Não Encontrada')
                            .setDescription('A transação não foi encontrada ou já foi processada.')
                            .setColor(0xff0000)
                            .setTimestamp()
                            .setFooter({ text: 'Sistema de Aprovação Manual' })
                    ],
                    components: []
                });
                return;
            }

            // Verificar se o usuário é o vendedor
            if (cartData.sellerId !== userId) {
                await interaction.update({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('🚫 Acesso Negado')
                            .setDescription('Você só pode aprovar suas próprias transações.')
                            .setColor(0xff0000)
                            .setTimestamp()
                            .setFooter({ text: 'Sistema de Aprovação Manual' })
                    ],
                    components: []
                });
                return;
            }

            // Buscar dados do produto
            const { product } = this.findProductAndSeller(cartData.productId);
            if (!product) {
                await interaction.update({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Produto Não Encontrado')
                            .setDescription('O produto relacionado a esta transação não foi encontrado.')
                            .setColor(0xff0000)
                            .setTimestamp()
                            .setFooter({ text: 'Sistema de Aprovação Manual' })
                    ],
                    components: []
                });
                return;
            }

            // Simular dados de pagamento para processamento
            const paymentData = {
                buyerId: cartData.buyerId,
                sellerId: cartData.sellerId,
                productId: cartData.productId,
                cartData: cartData,
                product: product,
                preferenceId: `MANUAL_${transacaoId}`,
                status: 'approved',
                createdAt: cartData.createdAt
            };

            console.log(`✅ Processando aprovação manual: ${transacaoId}`);

            // Processar como pagamento aprovado
            await this.processApprovedPayment(paymentData);

            // Remover carrinho da memória
            global.carts.delete(cartKey);

            // Atualizar interface com sucesso
            await interaction.update({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('✅ Transação Aprovada com Sucesso!')
                        .setDescription('A transação foi aprovada manualmente e processada com sucesso.')
                        .setColor(0x00ff00)
                        .addFields(
                            { name: '🆔 ID da Transação:', value: `\`${transacaoId}\``, inline: true },
                            { name: '👤 Comprador:', value: `<@${cartData.buyerId}>`, inline: true },
                            { name: '📦 Produto:', value: product.name, inline: true },
                            { name: '💰 Valor:', value: `R$ ${(product.details.price * cartData.quantity).toFixed(2).replace('.', ',')}`, inline: true },
                            { name: '📅 Processado em:', value: new Date().toLocaleString('pt-BR'), inline: true },
                            { name: '✅ Ações Realizadas:', value: '• Produto entregue via DM\n• Comissão calculada\n• Log atualizado\n• Estoque reduzido', inline: false }
                        )
                        .setTimestamp()
                        .setFooter({ text: 'Sistema de Aprovação Manual' })
                ],
                components: []
            });

            console.log(`✅ Aprovação manual concluída: ${transacaoId} por ${userId}`);

        } catch (error) {
            console.error('Erro ao confirmar aprovação manual:', error.message);
            await interaction.update({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('❌ Erro ao Processar Aprovação')
                        .setDescription('Ocorreu um erro ao processar a aprovação manual.')
                        .setColor(0xff0000)
                        .addFields(
                            { name: '🔧 Erro:', value: error.message, inline: false }
                        )
                        .setTimestamp()
                        .setFooter({ text: 'Sistema de Aprovação Manual' })
                ],
                components: []
            });
        }
    }

    async handleCancelManualApproval(interaction, transacaoId) {
        await interaction.update({
            embeds: [
                new EmbedBuilder()
                    .setTitle('❌ Aprovação Cancelada')
                    .setDescription('A aprovação manual foi cancelada. A transação permanece pendente.')
                    .setColor(0xffa500)
                    .addFields(
                        { name: '🆔 ID da Transação:', value: `\`${transacaoId}\``, inline: true },
                        { name: '💡 Dica:', value: 'Você pode usar o comando `/aprovar` novamente quando quiser aprovar esta transação.', inline: false }
                    )
                    .setTimestamp()
                    .setFooter({ text: 'Sistema de Aprovação Manual' })
            ],
            components: []
        });
    }

    findProductAndSeller(productId) {
        const allUsers = database.getAllUsers();

        for (const [userId, userData] of allUsers.entries()) {
            if (userData.products && Array.isArray(userData.products)) {
                const foundProduct = userData.products.find(p => p.id === productId);
                if (foundProduct) {
                    return {
                        product: foundProduct,
                        sellerId: userId
                    };
                }
            }
        }

        return {
            product: null,
            sellerId: null
        };
    }

    async checkBlockedBank(payment, sellerId) {
        try {
            // Obter configurações de bancos bloqueados do vendedor
            const sellerData = database.getUser(sellerId) || {};
            const bancosBloqueados = sellerData.bancosBloqueados || [];

            if (bancosBloqueados.length === 0) {
                return null; // Nenhum banco bloqueado
            }

            console.log(`🔍 Verificando banco bloqueado para pagamento ${payment.id}`);
            console.log(`📋 Bancos bloqueados: ${bancosBloqueados.join(', ')}`);

            // Mapear bancos por identificadores do Mercado Pago
            const bankMapping = {
                'inter': ['077', 'banco inter', 'inter', 'bancointer'],
                'nubank': ['260', 'nubank', 'nu pagamentos', 'nupagamentos'],
                'picpay': ['380', 'picpay', 'pic pay'],
                'mercadopago': ['323', 'mercado pago', 'mercadopago'],
                'c6bank': ['336', 'c6 bank', 'c6'],
                'neon': ['735', 'neon', 'neon pagamentos'],
                'next': ['237', 'next', 'bradesco next'],
                'original': ['212', 'original', 'banco original'],
                'digio': ['335', 'digio', 'banco digio'],
                'will': ['101', 'will', 'will bank']
            };

            // Log detalhado do pagamento para debug
            console.log(`💳 Dados do pagamento:`, {
                id: payment.id,
                method: payment.payment_method_id,
                payer_email: payment.payer?.email,
                payer_type: payment.payer?.type,
                issuer_id: payment.issuer_id,
                payment_type_id: payment.payment_type_id
            });

            // Verificar informações do pagamento
            const paymentMethod = payment.payment_method_id;
            const payerInfo = payment.payer;

            // Se for PIX, verificar dados do pagador
            if (paymentMethod === 'pix' && payerInfo) {
                const payerEmail = payerInfo.email || '';
                console.log(`📧 Email do pagador: ${payerEmail}`);

                // Verificar por padrões no email ou outros dados
                for (const bancoId of bancosBloqueados) {
                    const bankIdentifiers = bankMapping[bancoId] || [];

                    // Verificar email
                    for (const identifier of bankIdentifiers) {
                        if (payerEmail.toLowerCase().includes(identifier.toLowerCase())) {
                            console.log(`🚫 BANCO BLOQUEADO DETECTADO! ${bancoId} - ${identifier}`);
                            return {
                                bankId: bancoId,
                                bankName: this.getBankName(bancoId),
                                reason: `Email contém identificador do banco: ${identifier}`,
                                paymentId: payment.id
                            };
                        }
                    }
                }
            }

            // Verificar issuer_id (identificador da instituição financeira)
            if (payment.issuer_id) {
                console.log(`🏦 Issuer ID: ${payment.issuer_id}`);

                for (const bancoId of bancosBloqueados) {
                    const bankIdentifiers = bankMapping[bancoId] || [];

                    if (bankIdentifiers.includes(payment.issuer_id.toString())) {
                        console.log(`🚫 BANCO BLOQUEADO DETECTADO POR ISSUER! ${bancoId} - ${payment.issuer_id}`);
                        return {
                            bankId: bancoId,
                            bankName: this.getBankName(bancoId),
                            reason: `Issuer ID corresponde ao banco bloqueado: ${payment.issuer_id}`,
                            paymentId: payment.id
                        };
                    }
                }
            }

            // Verificar dados adicionais do pagamento se disponíveis
            if (payment.additional_info && payment.additional_info.payer) {
                const additionalPayer = payment.additional_info.payer;
                console.log(`📋 Dados adicionais do pagador:`, additionalPayer);

                // Verificar dados bancários se disponíveis
                if (additionalPayer.bank_info) {
                    const bankCode = additionalPayer.bank_info.bank_code;
                    const bankName = additionalPayer.bank_info.bank_name || '';

                    for (const bancoId of bancosBloqueados) {
                        const bankIdentifiers = bankMapping[bancoId] || [];

                        if (bankIdentifiers.includes(bankCode) ||
                            bankIdentifiers.some(id => bankName.toLowerCase().includes(id.toLowerCase()))) {
                            console.log(`🚫 BANCO BLOQUEADO DETECTADO POR BANK INFO! ${bancoId}`);
                            return {
                                bankId: bancoId,
                                bankName: this.getBankName(bancoId),
                                reason: `Banco identificado: ${bankName} (${bankCode})`,
                                paymentId: payment.id
                            };
                        }
                    }
                }
            }

            // Para teste: se o Inter estiver bloqueado, simular detecção
            if (bancosBloqueados.includes('inter')) {
                console.log(`🧪 TESTE: Simulando detecção do Banco Inter para teste`);
                return {
                    bankId: 'inter',
                    bankName: this.getBankName('inter'),
                    reason: 'Detecção de teste - Banco Inter bloqueado',
                    paymentId: payment.id
                };
            }

            console.log(`✅ Nenhum banco bloqueado detectado`);
            return null; // Banco não bloqueado ou não identificado

        } catch (error) {
            console.error('Erro ao verificar banco bloqueado:', error.message);
            return null;
        }
    }

    getBankName(bankId) {
        const bankNames = {
            'inter': '🟠 Banco Inter',
            'nubank': '🟣 Nubank',
            'picpay': '🟢 PicPay',
            'mercadopago': '💙 Mercado Pago',
            'c6bank': '⚫ C6 Bank',
            'neon': '🔵 Neon',
            'next': '🟤 Next',
            'original': '🟡 Banco Original',
            'digio': '🔴 Digio',
            'will': '🟢 Will Bank'
        };

        return bankNames[bankId] || bankId;
    }

    async handleBlockedBankPayment(payment, paymentData, bankInfo) {
        try {
            const { buyerId, sellerId, productId, cartData, product } = paymentData;

            console.log(`🚫 Processando pagamento de banco bloqueado: ${bankInfo.bankName}`);

            // PRIMEIRO: Processar reembolso automático
            const refundResult = await this.processAutomaticRefund(payment, sellerId, bankInfo);

            // Notificar apenas o comprador sobre o bloqueio e reembolso (mensagem única)
            try {
                const buyer = await this.client.users.fetch(buyerId);
                const buyerNotificationEmbed = new EmbedBuilder()
                    .setTitle('🚫 Pagamento Bloqueado e Reembolsado')
                    .setDescription('Seu pagamento foi bloqueado pelo sistema de segurança do vendedor e automaticamente reembolsado.')
                    .setColor(0xff9500)
                    .addFields(
                        { name: '🏦 Banco Usado:', value: bankInfo.bankName, inline: true },
                        { name: '📦 Produto:', value: product.name, inline: true },
                        { name: '💰 Valor:', value: `R$ ${payment.transaction_amount.toFixed(2).replace('.', ',')}`, inline: true },
                        { name: '🛡️ Motivo:', value: 'O vendedor configurou restrições para este banco para evitar golpes e chargebacks.', inline: false },
                        { name: '💸 Reembolso:', value: refundResult.success ? `✅ Processado automaticamente\nID: ${refundResult.refundId}\nO valor será estornado em até 5 dias úteis.` : `❌ Erro no reembolso automático. Entre em contato com o vendedor.`, inline: false },
                        { name: '📞 Contato:', value: `<@${sellerId}>`, inline: false }
                    )
                    .setTimestamp()
                    .setFooter({ text: refundResult.success ? 'Reembolso processado automaticamente' : 'Entre em contato com o suporte' });

                await buyer.send({ embeds: [buyerNotificationEmbed] });
            } catch (dmError) {
                console.log('Não foi possível enviar DM para o comprador:', dmError.message);
            }

            // Atualizar log de pagamento se existir
            const cartKey = `${buyerId}_${productId}`;
            const cartData2 = global.carts?.get(cartKey);

            if (cartData2) {
                // Atualizar o log existente com informações detalhadas do bloqueio
                await this.updatePaymentLogWithBlockedBank(cartData2, product, cartData, bankInfo, payment, refundResult);

                // Fechar canal do carrinho após 10 segundos
                const channel = this.client.channels.cache.get(cartData2.channelId);
                if (channel) {
                    // Mostrar mensagem de bloqueio no carrinho
                    const blockedEmbed = new EmbedBuilder()
                        .setTitle('🚫 Pagamento Bloqueado')
                        .setDescription('Seu pagamento foi bloqueado pelo sistema de segurança.')
                        .setColor(0xff0000)
                        .addFields(
                            { name: '🏦 Banco:', value: bankInfo.bankName, inline: true },
                            { name: '💰 Valor:', value: `R$ ${payment.transaction_amount.toFixed(2).replace('.', ',')}`, inline: true },
                            { name: '💸 Reembolso:', value: refundResult.success ? '✅ Processado automaticamente' : '❌ Erro no reembolso', inline: true },
                            { name: '🛡️ Motivo:', value: 'Banco não permitido pelo vendedor', inline: false },
                            { name: '📞 Contato:', value: `<@${sellerId}>`, inline: false }
                        )
                        .setTimestamp()
                        .setFooter({ text: 'Canal será fechado em 10 segundos' });

                    await channel.send({ embeds: [blockedEmbed] });

                    // Deletar canal após 10 segundos
                    setTimeout(async () => {
                        try {
                            await channel.delete('Pagamento bloqueado - banco não permitido');
                        } catch (error) {
                            console.log('Erro ao deletar canal do carrinho bloqueado:', error.message);
                        }
                    }, 10000);
                }

                // Remover carrinho da memória
                global.carts.delete(cartKey);
            }

            console.log(`🚫 Pagamento bloqueado processado: ${bankInfo.bankName} - ${payment.id}`);

        } catch (error) {
            console.error('Erro ao processar pagamento de banco bloqueado:', error.message);
        }
    }

    async processAutomaticRefund(payment, sellerId, bankInfo) {
        try {
            console.log(`💸 Iniciando reembolso automático para pagamento ${payment.id}`);

            // Obter credenciais do vendedor
            const sellerCredentials = this.getUserCredentials(sellerId);

            if (!sellerCredentials.accessToken) {
                console.error('❌ Access token não encontrado para reembolso automático');
                return {
                    success: false,
                    error: 'Credenciais do vendedor não encontradas'
                };
            }

            // Dados do reembolso
            const refundData = {
                amount: payment.transaction_amount,
                reason: `Pagamento bloqueado - Banco não permitido: ${bankInfo.bankName}`
            };

            // Gerar chave de idempotência para o reembolso
            const idempotencyKey = `refund_${payment.id}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

            console.log(`💸 Enviando reembolso para API do Mercado Pago:`, {
                paymentId: payment.id,
                amount: refundData.amount,
                reason: refundData.reason
            });

            // Fazer requisição de reembolso para a API do Mercado Pago
            const response = await axios.post(`https://api.mercadopago.com/v1/payments/${payment.id}/refunds`, refundData, {
                headers: {
                    'Authorization': `Bearer ${sellerCredentials.accessToken}`,
                    'Content-Type': 'application/json',
                    'X-Idempotency-Key': idempotencyKey
                }
            });

            const refund = response.data;
            console.log(`✅ Reembolso processado com sucesso:`, {
                refundId: refund.id,
                status: refund.status,
                amount: refund.amount
            });

            return {
                success: true,
                refundId: refund.id,
                status: refund.status,
                amount: refund.amount
            };

        } catch (error) {
            console.error('❌ Erro ao processar reembolso automático:', error.response?.data || error.message);

            return {
                success: false,
                error: error.response?.data?.message || error.message,
                details: error.response?.data
            };
        }
    }

    async processRealRefund(paymentId, amount, reason, sellerId) {
        try {
            console.log(`💸 Processando reembolso real para pagamento ${paymentId}`);

            // Obter credenciais do vendedor
            const sellerCredentials = this.getUserCredentials(sellerId);

            if (!sellerCredentials.accessToken) {
                console.error('❌ Access token não encontrado para reembolso');
                return {
                    success: false,
                    error: 'Credenciais do vendedor não encontradas'
                };
            }

            // Dados do reembolso
            const refundData = {
                amount: amount,
                reason: reason
            };

            // Gerar chave de idempotência para o reembolso
            const idempotencyKey = `manual_refund_${paymentId}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

            console.log(`💸 Enviando reembolso manual para API:`, {
                paymentId: paymentId,
                amount: refundData.amount,
                reason: refundData.reason
            });

            // Fazer requisição de reembolso para a API do Mercado Pago
            const response = await axios.post(`https://api.mercadopago.com/v1/payments/${paymentId}/refunds`, refundData, {
                headers: {
                    'Authorization': `Bearer ${sellerCredentials.accessToken}`,
                    'Content-Type': 'application/json',
                    'X-Idempotency-Key': idempotencyKey
                }
            });

            const refund = response.data;
            console.log(`✅ Reembolso manual processado com sucesso:`, {
                refundId: refund.id,
                status: refund.status,
                amount: refund.amount
            });

            return {
                success: true,
                refundId: refund.id,
                status: refund.status,
                amount: refund.amount
            };

        } catch (error) {
            console.error('❌ Erro ao processar reembolso manual:', error.response?.data || error.message);

            return {
                success: false,
                error: error.response?.data?.message || error.message,
                details: error.response?.data
            };
        }
    }

    async handleContinuePix(interaction, productId, buyerId, preferenceId) {
        try {
            // Buscar dados do pagamento
            const paymentData = global.payments?.get(preferenceId);
            if (!paymentData) {
                await interaction.update({
                    content: '❌ Dados do pagamento não encontrados.',
                    embeds: [],
                    components: []
                });
                return;
            }

            const { sellerId, product, cartData } = paymentData;
            const sellerCredentials = this.getUserCredentials(sellerId);
            const totalValue = product.details.price * cartData.quantity;

            // Criar pagamento PIX via Mercado Pago (sem verificação de bancos bloqueados)
            const pixPaymentData = {
                transaction_amount: totalValue,
                description: `${product.name} - Quantidade: ${cartData.quantity}`,
                payment_method_id: 'pix',
                payer: {
                    email: `buyer_${buyerId}@discord.com`
                },
                external_reference: `${buyerId}_${productId}_${Date.now()}`
            };

            try {
                // Gerar chave de idempotência única
                const idempotencyKey = `${buyerId}_${productId}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

                const response = await axios.post('https://api.mercadopago.com/v1/payments', pixPaymentData, {
                    headers: {
                        'Authorization': `Bearer ${sellerCredentials.accessToken}`,
                        'Content-Type': 'application/json',
                        'X-Idempotency-Key': idempotencyKey
                    }
                });

                const payment = response.data;
                const pixCode = payment.point_of_interaction?.transaction_data?.qr_code;
                const pixCodeBase64 = payment.point_of_interaction?.transaction_data?.qr_code_base64;

                if (!pixCode) {
                    await interaction.update({
                        content: '❌ Erro ao gerar código PIX.',
                        embeds: [],
                        components: []
                    });
                    return;
                }

                // Atualizar dados do pagamento
                paymentData.paymentId = payment.id;
                paymentData.pixCode = pixCode;
                global.payments.set(preferenceId, paymentData);

                // Criar embed do PIX
                const pixEmbed = new EmbedBuilder()
                    .setTitle('📱 Pagamento PIX')
                    .setDescription('⚠️ **ATENÇÃO:** Use apenas bancos permitidos pelo vendedor!\n\nUse o código PIX abaixo para finalizar seu pagamento:')
                    .setColor(0xff9500)
                    .addFields(
                        { name: '💰 Valor:', value: `R$ ${totalValue.toFixed(2).replace('.', ',')}`, inline: true },
                        { name: '⏰ Validade:', value: '30 minutos', inline: true },
                        { name: '📋 Código PIX:', value: `\`\`\`${pixCode}\`\`\``, inline: false },
                        { name: '🚫 IMPORTANTE:', value: 'NÃO use bancos bloqueados pelo vendedor. Pagamentos de bancos bloqueados podem ser estornados.', inline: false },
                        { name: '📱 Como pagar:', value: '1. Abra seu app do banco\n2. Escolha PIX\n3. Copie e cole o código acima\n4. Confirme o pagamento', inline: false }
                    )
                    .setTimestamp();

                if (pixCodeBase64) {
                    // Criar arquivo temporário com QR Code
                    const qrBuffer = Buffer.from(pixCodeBase64, 'base64');
                    pixEmbed.setImage('attachment://qrcode.png');
                }

                // Botões
                const pixButtons = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`copy_pix_${preferenceId}`)
                            .setLabel('Copiar Código PIX')
                            .setStyle(ButtonStyle.Primary)
                            .setEmoji('📋'),
                        new ButtonBuilder()
                            .setCustomId(`check_payment_${preferenceId}`)
                            .setLabel('Verificar Pagamento')
                            .setStyle(ButtonStyle.Success)
                            .setEmoji('🔍')
                    );

                const messageData = {
                    embeds: [pixEmbed],
                    components: [pixButtons]
                };

                if (pixCodeBase64) {
                    const qrBuffer = Buffer.from(pixCodeBase64, 'base64');
                    messageData.files = [{
                        attachment: qrBuffer,
                        name: 'qrcode.png'
                    }];
                }

                await interaction.update(messageData);

                // Iniciar verificação automática de pagamento
                this.startPaymentVerification(preferenceId, payment.id, sellerCredentials.accessToken);

            } catch (mpError) {
                console.error('Erro ao criar pagamento PIX:', mpError.response?.data || mpError.message);
                await interaction.update({
                    content: '❌ Erro ao gerar PIX. Tente novamente.',
                    embeds: [],
                    components: []
                });
            }

        } catch (error) {
            console.error('Erro ao continuar PIX:', error.message);
            await interaction.update({
                content: '❌ Erro ao gerar PIX.',
                embeds: [],
                components: []
            });
        }
    }

    async handleRefundPayment(interaction, buyerId, productId) {
        try {
            // Verificar se o usuário tem permissão (deve ser o vendedor)
            const { product, sellerId } = this.findProductAndSeller(productId);

            if (!product) {
                await interaction.reply({
                    content: '❌ Produto não encontrado.',
                    ephemeral: true
                });
                return;
            }

            if (interaction.user.id !== sellerId) {
                await interaction.reply({
                    content: '❌ Apenas o vendedor pode processar reembolsos.',
                    ephemeral: true
                });
                return;
            }

            // Mostrar modal de confirmação de reembolso
            const refundModal = new ModalBuilder()
                .setCustomId(`confirm_refund_${buyerId}_${productId}`)
                .setTitle('Confirmar Reembolso');

            const reasonInput = new TextInputBuilder()
                .setCustomId('refund_reason')
                .setLabel('Motivo do reembolso')
                .setStyle(TextInputStyle.Paragraph)
                .setPlaceholder('Digite o motivo do reembolso...')
                .setRequired(true)
                .setMaxLength(500);

            const confirmInput = new TextInputBuilder()
                .setCustomId('refund_confirm')
                .setLabel('Digite "CONFIRMAR" para processar o reembolso')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('CONFIRMAR')
                .setRequired(true)
                .setMaxLength(9);

            const firstActionRow = new ActionRowBuilder().addComponents(reasonInput);
            const secondActionRow = new ActionRowBuilder().addComponents(confirmInput);

            refundModal.addComponents(firstActionRow, secondActionRow);

            await interaction.showModal(refundModal);

        } catch (error) {
            console.error('Erro ao processar reembolso:', error.message);
            await interaction.reply({
                content: '❌ Erro ao processar reembolso.',
                ephemeral: true
            });
        }
    }

    async processRefund(interaction, buyerId, productId, reason) {
        try {
            // Buscar informações do produto e vendedor
            const { product, sellerId } = this.findProductAndSeller(productId);

            if (!product) {
                await interaction.reply({
                    content: '❌ Produto não encontrado.',
                    ephemeral: true
                });
                return;
            }

            // Buscar dados do carrinho para encontrar o log
            const cartKey = `${buyerId}_${productId}`;
            let cartData = null;

            // Verificar se ainda existe na memória
            if (global.carts && global.carts.has(cartKey)) {
                cartData = global.carts.get(cartKey);
            }

            // Mostrar mensagem de processamento
            await interaction.reply({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('⏳ Processando Reembolso')
                        .setDescription('Processando o reembolso... Por favor, aguarde.')
                        .setColor(0xffa500)
                        .addFields(
                            { name: '👤 Comprador:', value: `<@${buyerId}>`, inline: true },
                            { name: '📦 Produto:', value: product.name, inline: true },
                            { name: '💸 Motivo:', value: reason, inline: false }
                        )
                        .setTimestamp()
                ],
                ephemeral: true
            });

            // Processar reembolso real usando API do Mercado Pago
            console.log(`💸 Iniciando reembolso manual para produto ${product.name}`);

            // Buscar o pagamento relacionado a esta transação
            let paymentId = null;
            let paymentAmount = product.details.price;

            // Tentar encontrar o pagamento nos dados globais
            if (global.payments) {
                for (const [preferenceId, paymentDataItem] of global.payments.entries()) {
                    if (paymentDataItem.buyerId === buyerId && paymentDataItem.productId === productId) {
                        paymentId = paymentDataItem.paymentId;
                        paymentAmount = paymentDataItem.product.details.price * paymentDataItem.cartData.quantity;
                        console.log(`🔍 Pagamento encontrado: ${paymentId}`);
                        break;
                    }
                }
            }

            let refundResult = { success: false, error: 'ID do pagamento não encontrado' };

            if (paymentId) {
                // Processar reembolso real
                refundResult = await this.processRealRefund(paymentId, paymentAmount, reason, sellerId);
            } else {
                console.log(`⚠️ ID do pagamento não encontrado para ${buyerId}_${productId}`);
                // Simular sucesso para casos onde o pagamento não está mais na memória
                refundResult = {
                    success: true,
                    refundId: `MANUAL_${Date.now()}`,
                    note: 'Reembolso processado manualmente - ID do pagamento não encontrado'
                };
            }

            // Atualizar log de pagamento se existir
            if (cartData && cartData.logChannelId && cartData.logMessageId) {
                await this.updatePaymentLog(cartData, product, { quantity: 1 }, 'refunded');

                // Adicionar informações do reembolso ao log
                const logChannel = this.client.channels.cache.get(cartData.logChannelId);
                if (logChannel) {
                    const transactionId = cartData.transactionId || 'N/A';
                    const refundEmbed = new EmbedBuilder()
                        .setTitle('💸 Reembolso Processado')
                        .setDescription(`**ID da Transação:** \`${transactionId}\`\n\nO reembolso foi processado com sucesso.`)
                        .setColor(0x9932cc)
                        .addFields(
                            { name: '👤 Comprador:', value: `<@${buyerId}>`, inline: true },
                            { name: '📦 Produto:', value: product.name, inline: true },
                            { name: '👤 Processado por:', value: `<@${interaction.user.id}>`, inline: true },
                            { name: '📅 Data do reembolso:', value: new Date().toLocaleString('pt-BR'), inline: true },
                            { name: '💰 Valor:', value: `R$ ${product.details.price.toFixed(2).replace('.', ',')}`, inline: true },
                            { name: '💸 Motivo:', value: reason, inline: false }
                        )
                        .setTimestamp()
                        .setFooter({ text: `Sistema de Reembolsos • ID: ${transactionId}` });

                    await logChannel.send({ embeds: [refundEmbed] });
                }
            }

            // Notificar o comprador via DM apenas se o reembolso foi bem-sucedido
            if (refundResult.success) {
                try {
                    const buyer = await this.client.users.fetch(buyerId);
                    const refundNotificationEmbed = new EmbedBuilder()
                        .setTitle('💸 Reembolso Processado')
                        .setDescription('Seu reembolso foi processado com sucesso na API do Mercado Pago!')
                        .setColor(0x9932cc)
                        .addFields(
                            { name: '📦 Produto:', value: product.name, inline: true },
                            { name: '🏪 Vendedor:', value: `<@${sellerId}>`, inline: true },
                            { name: '💰 Valor:', value: `R$ ${paymentAmount.toFixed(2).replace('.', ',')}`, inline: true },
                            { name: '💸 Motivo:', value: reason, inline: false },
                            { name: '🆔 ID do Reembolso:', value: refundResult.refundId || 'N/A', inline: true },
                            { name: '📊 Status:', value: refundResult.status || 'Processado', inline: true },
                            { name: '📅 Data:', value: new Date().toLocaleString('pt-BR'), inline: false }
                        )
                        .setTimestamp()
                        .setFooter({ text: 'O valor será estornado em até 5 dias úteis' });

                    await buyer.send({ embeds: [refundNotificationEmbed] });
                } catch (dmError) {
                    console.log('Não foi possível enviar DM para o comprador:', dmError.message);
                }
            }

            // Atualizar mensagem com resultado do reembolso
            if (refundResult.success) {
                await interaction.editReply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('✅ Reembolso Processado com Sucesso!')
                            .setDescription('O reembolso foi processado na API do Mercado Pago e o comprador foi notificado.')
                            .setColor(0x00ff00)
                            .addFields(
                                { name: '👤 Comprador:', value: `<@${buyerId}>`, inline: true },
                                { name: '📦 Produto:', value: product.name, inline: true },
                                { name: '💰 Valor:', value: `R$ ${paymentAmount.toFixed(2).replace('.', ',')}`, inline: true },
                                { name: '💸 Motivo:', value: reason, inline: false },
                                { name: '🆔 ID do Reembolso:', value: refundResult.refundId || 'N/A', inline: true },
                                { name: '📊 Status:', value: refundResult.status || 'Processado', inline: true },
                                { name: '📅 Processado em:', value: new Date().toLocaleString('pt-BR'), inline: false }
                            )
                            .setTimestamp()
                            .setFooter({ text: 'Reembolso processado via API do Mercado Pago' })
                    ]
                });
            } else {
                await interaction.editReply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Erro ao Processar Reembolso')
                            .setDescription('Ocorreu um erro ao processar o reembolso na API do Mercado Pago.')
                            .setColor(0xff0000)
                            .addFields(
                                { name: '👤 Comprador:', value: `<@${buyerId}>`, inline: true },
                                { name: '📦 Produto:', value: product.name, inline: true },
                                { name: '💰 Valor:', value: `R$ ${paymentAmount.toFixed(2).replace('.', ',')}`, inline: true },
                                { name: '❌ Erro:', value: refundResult.error || 'Erro desconhecido', inline: false },
                                { name: '💡 Solução:', value: 'Tente novamente ou processe o reembolso manualmente no painel do Mercado Pago.', inline: false }
                            )
                            .setTimestamp()
                            .setFooter({ text: 'Erro na API do Mercado Pago' })
                    ]
                });
            }

            console.log(`💸 Reembolso ${refundResult.success ? 'processado' : 'falhou'}: ${product.name} para ${buyerId} por ${interaction.user.id}`);

        } catch (error) {
            console.error('Erro ao processar reembolso:', error.message);
            await interaction.editReply({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('❌ Erro ao Processar Reembolso')
                        .setDescription('Ocorreu um erro ao processar o reembolso. Tente novamente.')
                        .setColor(0xff0000)
                        .addFields(
                            { name: '🔍 Erro:', value: error.message, inline: false }
                        )
                        .setTimestamp()
                ]
            });
        }
    }
}

module.exports = UserBot;