/**
 * SISTEMA DE SIMULAÇÃO PARA TESTES
 * 
 * Este arquivo simula completamente o comportamento do Mercado Pago
 * para testes sem precisar de contas reais
 */

class MercadoPagoSimulator {
    constructor() {
        this.transactions = new Map();
        this.accounts = new Map();
        this.systemBalance = 0;
        this.sellerBalances = new Map();
        
        // Simular contas de teste
        this.setupTestAccounts();
    }

    setupTestAccounts() {
        // Conta do sistema (sua)
        this.accounts.set('SYSTEM_MASTER', {
            id: 'SYSTEM_MASTER',
            email: '<EMAIL>',
            name: '<PERSON><PERSON><PERSON> de Vend<PERSON>',
            balance: 0,
            type: 'system'
        });

        // Conta de vendedor teste
        this.accounts.set('SELLER_TEST_001', {
            id: 'SELLER_TEST_001',
            email: '<EMAIL>',
            name: 'Vendedor Teste 1',
            balance: 0,
            type: 'seller'
        });

        // Conta de comprador teste
        this.accounts.set('BUYER_TEST_001', {
            id: 'BUYER_TEST_001',
            email: '<EMAIL>',
            name: 'Comprador Teste 1',
            balance: 1000, // R$ 1000 para testes
            type: 'buyer'
        });
    }

    // Simular criação de preferência
    async createPreference(paymentData, accessToken) {
        const preferenceId = `PREF_TEST_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
        
        // Extrair informações do split
        const totalAmount = paymentData.items[0].unit_price * paymentData.items[0].quantity;
        const commissionAmount = paymentData.marketplace_fee || 0;
        const sellerAmount = totalAmount - commissionAmount;

        const preference = {
            id: preferenceId,
            init_point: `https://sandbox.mercadopago.com.br/checkout/v1/redirect?pref_id=${preferenceId}`,
            sandbox_init_point: `https://sandbox.mercadopago.com.br/checkout/v1/redirect?pref_id=${preferenceId}`,
            items: paymentData.items,
            external_reference: paymentData.external_reference,
            marketplace_fee: commissionAmount,
            split_details: {
                total_amount: totalAmount,
                commission_amount: commissionAmount,
                seller_amount: sellerAmount,
                commission_rate: commissionAmount / totalAmount
            },
            status: 'active',
            created_at: new Date().toISOString()
        };

        this.transactions.set(preferenceId, {
            preference,
            status: 'pending',
            payment_id: null,
            created_at: new Date().toISOString()
        });

        console.log(`🧪 [SIMULAÇÃO] Preferência criada: ${preferenceId}`);
        console.log(`💰 [SIMULAÇÃO] Split: Total R$ ${totalAmount.toFixed(2)}, Comissão R$ ${commissionAmount.toFixed(2)}, Vendedor R$ ${sellerAmount.toFixed(2)}`);

        return { data: preference };
    }

    // Simular pagamento PIX
    async generatePixPayment(preferenceId) {
        const transaction = this.transactions.get(preferenceId);
        if (!transaction) {
            throw new Error('Preferência não encontrada');
        }

        const pixCode = `00020126580014br.gov.bcb.pix0136${Math.random().toString(36).substring(2, 38)}520400005303986540${transaction.preference.split_details.total_amount.toFixed(2)}5802BR5925SISTEMA DE VENDAS TESTE6009SAO PAULO62070503***6304`;
        
        // Simular QR Code (base64 fake)
        const qrCodeBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

        console.log(`📱 [SIMULAÇÃO] PIX gerado para preferência: ${preferenceId}`);
        console.log(`💳 [SIMULAÇÃO] Código PIX: ${pixCode.substring(0, 50)}...`);

        return {
            data: {
                qr_code: pixCode,
                qr_code_base64: qrCodeBase64,
                ticket_url: `https://www.mercadopago.com.br/payments/${preferenceId}/ticket?caller_id=123456&hash=abc123`
            }
        };
    }

    // Simular aprovação de pagamento
    async approvePayment(preferenceId, buyerId = 'BUYER_TEST_001') {
        const transaction = this.transactions.get(preferenceId);
        if (!transaction) {
            throw new Error('Transação não encontrada');
        }

        const paymentId = `PAY_TEST_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
        const splitDetails = transaction.preference.split_details;

        // Verificar saldo do comprador
        const buyerAccount = this.accounts.get(buyerId);
        if (buyerAccount.balance < splitDetails.total_amount) {
            throw new Error('Saldo insuficiente para teste');
        }

        // Processar split payment
        buyerAccount.balance -= splitDetails.total_amount;
        
        // Adicionar à conta do sistema
        const systemAccount = this.accounts.get('SYSTEM_MASTER');
        systemAccount.balance += splitDetails.commission_amount;
        this.systemBalance += splitDetails.commission_amount;

        // Adicionar à conta do vendedor (simular)
        const sellerId = 'SELLER_TEST_001';
        if (!this.sellerBalances.has(sellerId)) {
            this.sellerBalances.set(sellerId, 0);
        }
        this.sellerBalances.set(sellerId, this.sellerBalances.get(sellerId) + splitDetails.seller_amount);

        // Atualizar transação
        transaction.status = 'approved';
        transaction.payment_id = paymentId;
        transaction.approved_at = new Date().toISOString();
        transaction.split_processed = {
            system_received: splitDetails.commission_amount,
            seller_received: splitDetails.seller_amount,
            processed_at: new Date().toISOString()
        };

        console.log(`✅ [SIMULAÇÃO] Pagamento aprovado: ${paymentId}`);
        console.log(`💰 [SIMULAÇÃO] Sistema recebeu: R$ ${splitDetails.commission_amount.toFixed(2)}`);
        console.log(`💰 [SIMULAÇÃO] Vendedor recebeu: R$ ${splitDetails.seller_amount.toFixed(2)}`);

        return {
            data: {
                id: paymentId,
                status: 'approved',
                status_detail: 'accredited',
                transaction_amount: splitDetails.total_amount,
                marketplace_fee: splitDetails.commission_amount,
                net_received_amount: splitDetails.seller_amount,
                date_approved: new Date().toISOString(),
                external_reference: transaction.preference.external_reference
            }
        };
    }

    // Verificar status de pagamento
    async checkPaymentStatus(preferenceId) {
        const transaction = this.transactions.get(preferenceId);
        if (!transaction) {
            return { data: { status: 'not_found' } };
        }

        return {
            data: {
                id: transaction.payment_id || 'pending',
                status: transaction.status,
                preference_id: preferenceId,
                transaction_amount: transaction.preference.split_details.total_amount,
                marketplace_fee: transaction.preference.split_details.commission_amount,
                net_received_amount: transaction.preference.split_details.seller_amount,
                date_created: transaction.created_at,
                date_approved: transaction.approved_at || null
            }
        };
    }

    // Obter relatório de saldos
    getBalanceReport() {
        const report = {
            system_balance: this.systemBalance,
            seller_balances: Object.fromEntries(this.sellerBalances),
            total_transactions: this.transactions.size,
            approved_transactions: Array.from(this.transactions.values()).filter(t => t.status === 'approved').length,
            accounts: Object.fromEntries(this.accounts)
        };

        console.log('📊 [SIMULAÇÃO] Relatório de Saldos:');
        console.log(`💰 Saldo do Sistema: R$ ${this.systemBalance.toFixed(2)}`);
        console.log(`🏪 Saldos dos Vendedores:`, Object.fromEntries(this.sellerBalances));
        console.log(`📈 Total de Transações: ${this.transactions.size}`);

        return report;
    }

    // Simular webhook de notificação
    simulateWebhook(preferenceId, action = 'payment.updated') {
        const transaction = this.transactions.get(preferenceId);
        if (!transaction) return null;

        const webhook = {
            id: `WEBHOOK_${Date.now()}`,
            live_mode: false,
            type: 'payment',
            date_created: new Date().toISOString(),
            application_id: 'TEST_APP_123456',
            user_id: 'TEST_USER_123456',
            version: 1,
            api_version: 'v1',
            action: action,
            data: {
                id: transaction.payment_id || preferenceId
            }
        };

        console.log(`🔔 [SIMULAÇÃO] Webhook enviado: ${action} para ${preferenceId}`);
        return webhook;
    }
}

// Instância global do simulador
const mpSimulator = new MercadoPagoSimulator();

module.exports = {
    MercadoPagoSimulator,
    mpSimulator
};
