# 🏗️ Sistema de Comissão Real - Guia de Configuração

## 📋 Visão Geral

Este sistema implementa **Split Payments** do Mercado Pago para dividir automaticamente os pagamentos entre:
- **Vendedor**: Recebe 92% do valor
- **<PERSON><PERSON><PERSON> (Você)**: Recebe 8% de comissão

## 🔐 1. Configuração das Suas Credenciais (Dono do Bot)

### Edite o arquivo `system-credentials.js`:

```javascript
const SYSTEM_CREDENTIALS = {
    // SUAS credenciais do Mercado Pago (dono do bot)
    MASTER_ACCESS_TOKEN: 'APP_USR-SEU_ACCESS_TOKEN_AQUI',
    MASTER_CLIENT_SECRET: 'SEU_CLIENT_SECRET_AQUI', 
    MASTER_CLIENT_ID: 'SEU_CLIENT_ID_AQUI',
    
    // Taxa de comissão (8%)
    COMMISSION_RATE: 0.08,
    
    // Informações da conta que recebe comissões
    COMMISSION_RECEIVER: {
        email: '<EMAIL>',
        name: '<PERSON>u Nome ou Empresa',
        description: 'Comissão do Sistema de Vendas'
    }
};
```

### ⚠️ IMPORTANTE - Segurança:
1. **NUNCA** compartilhe essas credenciais
2. Adicione `system-credentials.js` ao `.gitignore`
3. Essas credenciais são APENAS suas e inacessíveis aos vendedores

## 🏪 2. Como Funciona para os Vendedores

### Os vendedores continuam configurando normalmente:
1. Usam `/autenticacao` 
2. Configuram suas próprias credenciais do Mercado Pago
3. **MAS**: Os pagamentos são processados pelo SEU Mercado Pago

### Split Automático:
- **Cliente paga**: R$ 100,00
- **Você recebe**: R$ 8,00 (comissão)
- **Vendedor recebe**: R$ 92,00 (valor líquido)

## 💳 3. Configuração no Mercado Pago

### No seu painel do Mercado Pago:

1. **Ative Marketplace**:
   - Vá em "Configurações" → "Marketplace"
   - Ative a funcionalidade de marketplace
   - Configure webhooks para notificações

2. **Configure Webhooks**:
   ```
   URL de Pagamentos: https://seu-dominio.com/webhook/payment
   URL de Split: https://seu-dominio.com/webhook/split
   Eventos: payment, merchant_order
   ```

3. **Permissões Necessárias**:
   - `read` - Ler informações de pagamentos
   - `write` - Criar preferências e processar pagamentos
   - `offline_access` - Acesso contínuo

## 🔄 4. Fluxo de Pagamento

### Quando um cliente compra:

1. **Sistema cria preferência** usando SUAS credenciais
2. **Mercado Pago processa** o pagamento
3. **Split automático**:
   - 8% vai para sua conta
   - 92% vai para conta do vendedor
4. **Produto entregue** automaticamente
5. **Logs registrados** com detalhes do split

## 📊 5. Monitoramento de Comissões

### Logs Detalhados:
```javascript
// Exemplo de log de split payment
{
    transactionId: "TXN-1703025598000-A7B9C2",
    totalAmount: 100.00,
    commissionAmount: 8.00,
    sellerAmount: 92.00,
    commissionRate: 0.08,
    status: "approved",
    splitDetails: {
        systemReceived: 8.00,
        sellerReceived: 92.00,
        processingFee: 0.00
    }
}
```

### Relatórios Automáticos:
- Total de comissões recebidas
- Número de transações processadas
- Vendedores mais ativos
- Produtos mais vendidos

## 🛡️ 6. Segurança e Proteção

### Suas Credenciais:
- ✅ **Protegidas** no arquivo `system-credentials.js`
- ✅ **Inacessíveis** aos vendedores
- ✅ **Criptografadas** em produção
- ✅ **Backup seguro** recomendado

### Credenciais dos Vendedores:
- ✅ **Armazenadas** no database local
- ✅ **Usadas apenas** para identificação
- ✅ **Não expostas** no código
- ✅ **Validadas** antes do uso

## 🚀 7. Vantagens do Sistema

### Para Você (Dono do Bot):
- 💰 **Comissão automática** de 8%
- 🔐 **Controle total** do sistema
- 📊 **Relatórios detalhados**
- 🛡️ **Segurança máxima**

### Para os Vendedores:
- 💳 **Recebimento direto** na conta
- 📱 **Interface simples** de configuração
- 🚀 **Vendas automatizadas**
- 📊 **Transparência** nos valores

### Para os Compradores:
- 🔒 **Pagamento seguro** via Mercado Pago
- 📱 **PIX instantâneo**
- 🌐 **Site oficial** do MP
- ✅ **Entrega automática**

## ⚙️ 8. Configurações Avançadas

### Personalizar Taxa de Comissão:
```javascript
// No system-credentials.js
COMMISSION_RATE: 0.10, // 10%
COMMISSION_RATE: 0.05, // 5%
COMMISSION_RATE: 0.15, // 15%
```

### Configurar Limites:
```javascript
// Valor mínimo para split
MIN_SPLIT_AMOUNT: 10.00,

// Valor máximo por transação
MAX_TRANSACTION_AMOUNT: 5000.00,

// Limite diário por vendedor
DAILY_SELLER_LIMIT: 10000.00
```

## 🔧 9. Troubleshooting

### Erro: "Credenciais não configuradas"
- ✅ Verifique `system-credentials.js`
- ✅ Confirme access token válido
- ✅ Teste conexão com MP

### Erro: "Split payment falhou"
- ✅ Verifique se marketplace está ativo
- ✅ Confirme permissões da aplicação
- ✅ Teste com valor pequeno primeiro

### Erro: "Vendedor não recebeu"
- ✅ Verifique conta do vendedor
- ✅ Confirme dados bancários
- ✅ Aguarde processamento (até 24h)

## 📞 10. Suporte

### Em caso de problemas:
1. **Verifique logs** do sistema
2. **Teste com valores pequenos**
3. **Consulte documentação** do Mercado Pago
4. **Entre em contato** com suporte técnico

---

## ✅ Checklist de Configuração

- [ ] Credenciais do sistema configuradas
- [ ] Marketplace ativado no Mercado Pago
- [ ] Webhooks configurados
- [ ] Teste de split realizado
- [ ] Logs funcionando
- [ ] Backup das credenciais
- [ ] Sistema em produção

**🎉 Parabéns! Seu sistema de comissão real está configurado e funcionando!**
