[2025-06-19T03:12:57.666Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T03:13:02.825Z] GENERAL: Erro no botão back_to_config: row is not defined
Memory: N/A
Uptime: N/A
---
[2025-06-19T03:17:02.690Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T03:22:30.267Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T03:22:33.157Z] GENERAL: Erro no botão gerenciar_produtos: row1 is not defined
Memory: N/A
Uptime: N/A
---
[2025-06-19T03:24:59.507Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T03:27:08.028Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T03:27:38.849Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleGerenciarProdutos (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3808:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1481:32)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 25MB
Uptime: 43s
---
[2025-06-19T03:32:03.632Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T03:32:38.362Z] GENERAL: Erro no botão back_to_auth: row1 is not defined
Memory: N/A
Uptime: N/A
---
[2025-06-19T03:33:09.276Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleGerenciarProdutos (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3833:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1506:32)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 25MB
Uptime: 75s
---
[2025-06-19T19:05:59.804Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T19:32:05.552Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T19:38:10.712Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:00:36.563Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:01:02.434Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleGerenciarProdutos (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:5222:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:764:32)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 25MB
Uptime: 34s
---
[2025-06-19T20:05:17.809Z] GENERAL: Erro no servidor: listen EADDRINUSE: address already in use :::3003
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:05:32.922Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:05:33.186Z] GENERAL: Erro no comando configurar: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:05:36.535Z] GENERAL: Erro no botão config_loja: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:05:38.034Z] GENERAL: Erro no botão gerenciar_produtos: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:05:41.169Z] GENERAL: Erro no botão remover_produto: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:05:43.779Z] WRAPPEDASYNC: Unknown interaction
Stack: DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:361:22)
    at async UserBot.handleConfirmProductRemoval (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:4113:9)
    at async UserBot.handleSelectMenu (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1064:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:57:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 26MB
Uptime: 315s
---
[2025-06-19T20:05:43.781Z] UNCAUGHTEXCEPTION: Unknown interaction
Stack: DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:361:22)
    at async UserBot.handleConfirmProductRemoval (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:4113:9)
    at async UserBot.handleSelectMenu (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1064:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:57:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 26MB
Uptime: 315s
---
[2025-06-19T20:05:45.388Z] GENERAL: Erro no botão confirm_remove_product_1750363257286: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:05:46.291Z] GENERAL: Erro no botão confirm_remove_product_1750363257286: Unknown Message
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:05:55.903Z] GENERAL: Erro no comando configurar: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:05:57.498Z] GENERAL: Erro no botão config_loja: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:05:58.908Z] GENERAL: Erro no botão gerenciar_produtos: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:06:00.816Z] GENERAL: Erro no botão criar_produto: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:06:09.857Z] WRAPPEDASYNC: Unknown interaction
Stack: DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async ModalSubmitInteraction.deferUpdate (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:312:22)
    at async UserBot.handleModalSubmit (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1841:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:61:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 25MB
Uptime: 54s
---
[2025-06-19T20:06:09.859Z] UNCAUGHTEXCEPTION: Unknown interaction
Stack: DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async ModalSubmitInteraction.deferUpdate (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:312:22)
    at async UserBot.handleModalSubmit (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1841:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:61:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 25MB
Uptime: 54s
---
[2025-06-19T20:06:13.263Z] WRAPPEDASYNC: Interaction has already been acknowledged.
Stack: DiscordAPIError[40060]: Interaction has already been acknowledged.
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:361:22)
    at async UserBot.handleSelectMenu (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:772:17)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:57:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 27MB
Uptime: 345s
---
[2025-06-19T20:06:13.264Z] UNCAUGHTEXCEPTION: Interaction has already been acknowledged.
Stack: DiscordAPIError[40060]: Interaction has already been acknowledged.
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:361:22)
    at async UserBot.handleSelectMenu (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:772:17)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:57:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 27MB
Uptime: 345s
---
[2025-06-19T20:06:14.263Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleGerenciarProdutos (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:5497:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:680:32)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 26MB
Uptime: 58s
---
[2025-06-19T20:06:23.873Z] GENERAL: Erro no botão config_product_description_1750363572566: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:06:26.166Z] WRAPPEDASYNC: Interaction has already been acknowledged.
Stack: DiscordAPIError[40060]: Interaction has already been acknowledged.
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async ModalSubmitInteraction.reply (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:200:22)
    at async UserBot.handleUpdateProductConfigDetails (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3820:13)
    at async UserBot.handleModalSubmit (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1670:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:61:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 27MB
Uptime: 358s
---
[2025-06-19T20:06:26.168Z] UNCAUGHTEXCEPTION: Interaction has already been acknowledged.
Stack: DiscordAPIError[40060]: Interaction has already been acknowledged.
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async ModalSubmitInteraction.reply (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:200:22)
    at async UserBot.handleUpdateProductConfigDetails (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3820:13)
    at async UserBot.handleModalSubmit (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1670:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:61:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 27MB
Uptime: 358s
---
[2025-06-19T20:06:30.931Z] GENERAL: Erro no botão config_product_price_1750363572566: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:06:33.180Z] WRAPPEDASYNC: Interaction has already been acknowledged.
Stack: DiscordAPIError[40060]: Interaction has already been acknowledged.
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async ModalSubmitInteraction.reply (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:200:22)
    at async UserBot.handleUpdateProductConfigDetails (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3820:13)
    at async UserBot.handleModalSubmit (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1678:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:61:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 26MB
Uptime: 365s
---
[2025-06-19T20:06:33.182Z] UNCAUGHTEXCEPTION: Interaction has already been acknowledged.
Stack: DiscordAPIError[40060]: Interaction has already been acknowledged.
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async ModalSubmitInteraction.reply (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:200:22)
    at async UserBot.handleUpdateProductConfigDetails (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3820:13)
    at async UserBot.handleModalSubmit (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1678:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:61:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 26MB
Uptime: 365s
---
[2025-06-19T20:06:35.393Z] GENERAL: Erro no botão config_product_save_1750363572566: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:09:20.606Z] GENERAL: Erro no servidor: listen EADDRINUSE: address already in use :::3003
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:09:33.765Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:09:34.041Z] GENERAL: Erro no comando configurar: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:09:35.853Z] GENERAL: Erro no botão config_loja: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:09:37.454Z] GENERAL: Erro no botão gerenciar_produtos: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:09:50.081Z] GENERAL: Erro no botão remover_produto: Interaction has already been acknowledged.
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:09:52.518Z] WRAPPEDASYNC: Unknown interaction
Stack: DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:361:22)
    at async UserBot.handleConfirmProductRemoval (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:4471:9)
    at async UserBot.handleSelectMenu (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1065:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:57:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 25MB
Uptime: 34s
---
[2025-06-19T20:09:52.519Z] UNCAUGHTEXCEPTION: Unknown interaction
Stack: DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:361:22)
    at async UserBot.handleConfirmProductRemoval (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:4471:9)
    at async UserBot.handleSelectMenu (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1065:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:57:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 25MB
Uptime: 34s
---
[2025-06-19T20:09:53.858Z] GENERAL: Erro no botão confirm_remove_product_1750363257286: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:09:57.708Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ButtonInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleGerenciarProdutos (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:5222:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:4199:28)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 27MB
Uptime: 569s
---
[2025-06-19T20:10:05.895Z] GENERAL: Erro no comando configurar: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:10:07.313Z] GENERAL: Erro no botão config_loja: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:10:09.361Z] GENERAL: Erro no botão gerenciar_produtos: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:10:13.467Z] GENERAL: Erro no botão remover_produto: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:10:16.775Z] WRAPPEDASYNC: Unknown interaction
Stack: DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:361:22)
    at async UserBot.handleConfirmProductRemoval (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:4113:9)
    at async UserBot.handleSelectMenu (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1064:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:57:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 27MB
Uptime: 588s
---
[2025-06-19T20:10:16.777Z] UNCAUGHTEXCEPTION: Unknown interaction
Stack: DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async StringSelectMenuInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:361:22)
    at async UserBot.handleConfirmProductRemoval (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:4113:9)
    at async UserBot.handleSelectMenu (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:1064:13)
    at async C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:57:21
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\bot de vendas\anticrash-system.js:186:24)
Memory: 27MB
Uptime: 588s
---
[2025-06-19T20:10:18.053Z] GENERAL: Erro no botão confirm_remove_product_1750363572566: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:10:18.830Z] GENERAL: Erro no botão confirm_remove_product_1750363572566: Unknown Message
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:10:23.548Z] GENERAL: Erro no comando configurar: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:10:24.992Z] GENERAL: Erro no botão config_loja: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:10:26.437Z] GENERAL: Erro no botão gerenciar_produtos: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T20:53:41.517Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T21:01:50.738Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T21:07:46.419Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T21:09:39.059Z] GENERAL: Erro no botão confirm_delete_product_1750366446375: Unknown Message
Memory: N/A
Uptime: N/A
---
[2025-06-19T21:09:54.062Z] UNHANDLEDREJECTION: The reply to this interaction has already been sent or deferred.
Stack: Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ButtonInteraction.update (C:\Users\<USER>\Desktop\bot de vendas\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:342:46)
    at UserBot.handleGerenciarProdutos (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:5746:27)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\bot de vendas\user-bot-template.js:3075:28)
    at listOnTimeout (node:internal/timers:569:17)
    at process.processTimers (node:internal/timers:512:7)
Memory: 26MB
Uptime: 149s
---
[2025-06-19T21:26:55.401Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T21:35:10.754Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T21:41:16.007Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:05:17.375Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:08:38.708Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:11:20.985Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:24:50.737Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:28:49.682Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:29:56.461Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:34:08.467Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:50:29.179Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:51:04.486Z] GENERAL: Erro no botão confirm_remove_product_1750367416230: Unknown Message
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:56:15.761Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:56:28.406Z] GENERAL: Erro no botão cart_remove_product_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T22:57:23.063Z] GENERAL: Erro no botão cart_cancel_1750373484495_1195685295568470056: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:00:46.887Z] GENERAL: Erro no botão cart_cancel_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:27:52.032Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:28:28.254Z] GENERAL: Erro no botão buy_product_1750373484495: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:30:20.559Z] GENERAL: Erro no botão checkout_dm_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:38:44.336Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:39:02.734Z] GENERAL: Erro no botão cart_cancel_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:41:59.110Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:43:09.761Z] GENERAL: Erro no botão cart_cancel_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:47:00.121Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:49:22.370Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:51:43.921Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-19T23:51:54.272Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:07:12.295Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:16:17.780Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:24:52.096Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:24:52.467Z] GENERAL: Erro no botão buy_product_1750373484495: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:25:42.516Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:34:38.566Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:34:56.808Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:43:58.995Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:44:11.310Z] GENERAL: Erro no botão cart_cancel_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:44:47.589Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:58:02.150Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:58:02.479Z] GENERAL: Erro no botão buy_product_1750373484495: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-20T00:58:15.046Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
[2025-06-20T01:00:23.317Z] GENERAL: Erro no botão cart_cancel_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-20T01:06:22.957Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-20T01:06:52.185Z] GENERAL: Erro no botão cart_cancel_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-22T17:00:16.037Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-22T17:00:33.562Z] GENERAL: Erro no botão cart_cancel_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-22T17:08:27.545Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-22T17:08:32.547Z] GENERAL: Erro no botão buy_product_1750373484495: database.getGuild is not a function
Memory: N/A
Uptime: N/A
---
[2025-06-22T17:08:40.203Z] GENERAL: Erro no botão buy_product_1750373484495: database.getGuild is not a function
Memory: N/A
Uptime: N/A
---
[2025-06-22T17:12:07.246Z] GENERAL: Erro no botão buy_product_1750373484495: Assignment to constant variable.
Memory: N/A
Uptime: N/A
---
[2025-06-22T17:12:07.251Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-22T17:12:29.068Z] GENERAL: Erro no botão buy_product_1750373484495: Assignment to constant variable.
Memory: N/A
Uptime: N/A
---
[2025-06-22T21:03:02.185Z] GENERAL: Erro no botão buy_product_1750373484495: Assignment to constant variable.
Memory: N/A
Uptime: N/A
---
[2025-06-22T21:03:02.191Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-22T21:08:12.232Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-22T21:09:16.487Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
[2025-06-22T21:14:09.983Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
[2025-06-22T21:16:31.580Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-22T22:00:39.313Z] WARNING: Supplying "ephemeral" for interaction response options is deprecated. Utilize flags instead.
Memory: N/A
Uptime: N/A
---
[2025-06-22T22:02:56.959Z] GENERAL: Erro no botão cart_cancel_1750373484495_1202721227999944777: Unknown interaction
Memory: N/A
Uptime: N/A
---
[2025-06-22T22:04:49.877Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
[2025-06-22T22:10:54.241Z] WARNING: buffer.File is an experimental feature and might change at any time
Memory: N/A
Uptime: N/A
---
