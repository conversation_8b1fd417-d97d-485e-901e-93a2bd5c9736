/**
 * CREDENCIAIS DO SISTEMA - APENAS PARA O DONO DO BOT
 * 
 * IMPORTANTE: 
 * - Este arquivo contém suas credenciais pessoais do Mercado Pago
 * - NUNCA compartilhe essas informações
 * - Adicione este arquivo ao .gitignore
 * - Essas credenciais são usadas para receber comissões
 */

const SYSTEM_CREDENTIALS = {
    // SUAS credenciais REAIS do Mercado Pago (dono do bot)
    // ⚠️ SUBSTITUA pelos valores reais da sua aplicação:
    MASTER_ACCESS_TOKEN: 'APP_USR-SEU_ACCESS_TOKEN_AQUI', // Cole aqui seu Access Token REAL
    MASTER_CLIENT_SECRET: 'SEU_CLIENT_SECRET_AQUI',        // Cole aqui seu Client Secret REAL
    MASTER_CLIENT_ID: 'SEU_CLIENT_ID_AQUI',                // Cole aqui seu Client ID REAL
    
    // Configurações de comissão
    COMMISSION_RATE: 0.08, // 8%
    
    // Informações da conta que recebe comissões
    COMMISSION_RECEIVER: {
        email: '<EMAIL>',
        name: 'Seu Nome ou Empresa',
        description: 'Comissão do Sistema de Vendas'
    },
    
    // Configurações de segurança
    WEBHOOK_SECRET: 'sua-chave-secreta-webhook',
    
    // URLs de webhook (configure no painel do Mercado Pago)
    WEBHOOK_URLS: {
        payment: 'https://seu-dominio.com/webhook/payment',
        split: 'https://seu-dominio.com/webhook/split'
    }
};

// Função para validar se as credenciais estão configuradas
function validateSystemCredentials() {
    const required = [
        'MASTER_ACCESS_TOKEN',
        'MASTER_CLIENT_SECRET', 
        'MASTER_CLIENT_ID'
    ];
    
    for (const field of required) {
        if (!SYSTEM_CREDENTIALS[field] || SYSTEM_CREDENTIALS[field].includes('SEU_')) {
            throw new Error(`❌ Credencial do sistema não configurada: ${field}`);
        }
    }
    
    console.log('✅ Credenciais do sistema validadas com sucesso');
    return true;
}

// Função para obter credenciais (apenas para uso interno)
function getSystemCredentials() {
    validateSystemCredentials();
    return SYSTEM_CREDENTIALS;
}

// Função para obter apenas o access token (mais seguro)
function getSystemAccessToken() {
    validateSystemCredentials();
    return SYSTEM_CREDENTIALS.MASTER_ACCESS_TOKEN;
}

// Função para obter taxa de comissão
function getCommissionRate() {
    return SYSTEM_CREDENTIALS.COMMISSION_RATE;
}

module.exports = {
    getSystemCredentials,
    getSystemAccessToken,
    getCommissionRate,
    validateSystemCredentials
};
