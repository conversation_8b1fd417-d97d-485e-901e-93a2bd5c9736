/**
 * CREDENCIAIS DO SISTEMA - APENAS PARA O DONO DO BOT
 * 
 * IMPORTANTE: 
 * - Este arquivo contém suas credenciais pessoais do Mercado Pago
 * - NUNCA compartilhe essas informações
 * - Adicione este arquivo ao .gitignore
 * - Essas credenciais são usadas para receber comissões
 */

const SYSTEM_CREDENTIALS = {
    // 🔑 SUAS CREDENCIAIS (CONTA MASTER - DONO DO BOT)
    // ⚠️ COLE AQUI AS CREDENCIAIS DA SUA CONTA PESSOAL DO MERCADO PAGO:
    // Esta conta vai RECEBER as comissões de 8% automaticamente
    MASTER_ACCESS_TOKEN: 'APP_USR-COLE_SUA_CREDENCIAL_MASTER_AQUI',
    MASTER_CLIENT_SECRET: 'COLE_SEU_CLIENT_SECRET_MASTER_AQUI',
    MASTER_CLIENT_ID: 'COLE_SEU_CLIENT_ID_MASTER_AQUI',
    
    // Configurações de comissão
    COMMISSION_RATE: 0.08, // 8%
    
    // Informações da conta que recebe comissões
    COMMISSION_RECEIVER: {
        email: '<EMAIL>',        // ⚠️ COLE SEU EMAIL REAL AQUI
        name: 'Sidney Rogério Centioli',                    // ⚠️ COLE SEU NOME REAL AQUI
        description: 'Comissão do Sistema de Vendas',

        // � CONFIGURAÇÃO NUBANK - PIX AUTOMÁTICO:
        external_pix: '***********',  // ⚠️ COLE SUA CHAVE PIX DO NUBANK
        bank_account: {
            bank: 'NUBANK',                     // Nubank
            agency: '0001',                     // Agência padrão Nubank
            account: 'SUA_CONTA_NUBANK',        // Sua conta Nubank (opcional)
            account_type: 'CORRENTE'            // Conta corrente
        }
    },
    
    // Configurações de segurança
    WEBHOOK_SECRET: 'sua-chave-secreta-webhook',
    
    // URLs de webhook (configure no painel do Mercado Pago)
    WEBHOOK_URLS: {
        payment: 'https://seu-dominio.com/webhook/payment',
        split: 'https://seu-dominio.com/webhook/split'
    }
};

// Função para validar se as credenciais estão configuradas
function validateSystemCredentials() {
    const required = [
        'MASTER_ACCESS_TOKEN',
        'MASTER_CLIENT_SECRET', 
        'MASTER_CLIENT_ID'
    ];
    
    for (const field of required) {
        if (!SYSTEM_CREDENTIALS[field] || SYSTEM_CREDENTIALS[field].includes('SEU_')) {
            throw new Error(`❌ Credencial do sistema não configurada: ${field}`);
        }
    }
    
    console.log('✅ Credenciais do sistema validadas com sucesso');
    return true;
}

// Função para obter credenciais (apenas para uso interno)
function getSystemCredentials() {
    // Tentar usar credenciais configuradas, senão usar fallback do users.json
    try {
        validateSystemCredentials();
        return SYSTEM_CREDENTIALS;
    } catch (error) {
        // Fallback: usar credenciais do users.json
        const database = require('./database');
        const allUsers = database.getAllUsers();

        for (const [userId, userData] of allUsers.entries()) {
            if (userData.mercadoPagoAccessToken && userData.mercadoPagoConfirmed) {
                console.log('⚠️ Usando credenciais do users.json como fallback');
                return {
                    MASTER_ACCESS_TOKEN: userData.mercadoPagoAccessToken,
                    MASTER_CLIENT_SECRET: userData.mercadoPagoClientSecret || '',
                    COMMISSION_RATE: 0.08,
                    COMMISSION_RECEIVER: {
                        email: userData.mercadoPagoAccountInfo?.email || '<EMAIL>',
                        name: userData.mercadoPagoAccountInfo?.nickname || 'Sistema',
                        external_pix: '***********' // ⚠️ CONFIGURE SUA CHAVE PIX DO NUBANK AQUI
                    }
                };
            }
        }
        throw new Error('Nenhuma credencial encontrada');
    }
}

// Função para obter apenas o access token (mais seguro)
function getSystemAccessToken() {
    const credentials = getSystemCredentials();
    return credentials.MASTER_ACCESS_TOKEN;
}

// Função para obter taxa de comissão
function getCommissionRate() {
    return SYSTEM_CREDENTIALS.COMMISSION_RATE;
}

module.exports = {
    getSystemCredentials,
    getSystemAccessToken,
    getCommissionRate,
    validateSystemCredentials
};
